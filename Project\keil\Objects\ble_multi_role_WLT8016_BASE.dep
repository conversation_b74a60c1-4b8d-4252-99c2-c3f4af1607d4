Dependencies for Project 'ble_multi_role', Target 'WLT8016_BASE': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (..\code\UserKeyAdv.c)(0x68833668)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\userkeyadv.o --omf_browse .\objects\userkeyadv.crf --depend .\objects\userkeyadv.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\components\ble\include\gap\gap_api.h)(0x684A9BB1)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\ble\include\gatt\gatt_api.h)(0x684A9BB1)
I (..\..\components\modules\os\include\os_msg_q.h)(0x684A9BB6)
I (..\code\../../components/modules/os/include/os_timer.h)(0x684A9BB6)
I (..\..\components\modules\button\button.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\jump_table.h)(0x684A9BB6)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\os\include\os_mem.h)(0x684A9BB6)
I (..\..\components\modules\sys\include\sys_utils.h)(0x684A9BB6)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\code\UserApp.h)(0x685566C8)
I (..\code\UserBLE.h)(0x684A9BB1)
I (..\code\UserCustomer.h)(0x687A0174)
I (..\code\UserKeyAdv.h)(0x68776514)
I (..\code\UserKeyMatrix.h)(0x688323C5)
I (..\..\components\driver\include\driver_gpio.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\code\UserPrintf.h)(0x684A9BB1)
I (..\..\components\driver\include\driver_adc.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_efuse.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_flash.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_i2s.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu_regs.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_rtc.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_uart.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_wdt.h)(0x684A9BB8)
I (..\code\flash_usage_config.h)(0x68832D2E)
F (..\code\UserCustomer.c)(0x68831C64)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\usercustomer.o --omf_browse .\objects\usercustomer.crf --depend .\objects\usercustomer.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\components\ble\include\gap\gap_api.h)(0x684A9BB1)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\ble\include\gatt\gatt_api.h)(0x684A9BB1)
I (..\..\components\modules\os\include\os_msg_q.h)(0x684A9BB6)
I (..\..\components\modules\button\button.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\jump_table.h)(0x684A9BB6)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\os\include\os_mem.h)(0x684A9BB6)
I (..\..\components\modules\os\include\os_timer.h)(0x684A9BB6)
I (..\..\components\modules\sys\include\sys_utils.h)(0x684A9BB6)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\code\UserBLE.h)(0x684A9BB1)
I (..\..\components\driver\include\driver_efuse.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_flash.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_i2s.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_pmu.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu_regs.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_rtc.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_uart.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_wdt.h)(0x684A9BB8)
I (..\code\flash_usage_config.h)(0x68832D2E)
I (..\code\UserApp.h)(0x685566C8)
I (..\code\UserAppConfig.h)(0x68564626)
I (..\code\UserCustomer.h)(0x687A0174)
I (..\code\UserKeyAdv.h)(0x68776514)
I (..\code\UserKeyMatrix.h)(0x688323C5)
I (..\..\components\driver\include\driver_gpio.h)(0x684A9BB8)
I (..\code\UserPLA.h)(0x684A9BB1)
I (..\code\UserPrintf.h)(0x684A9BB1)
I (..\code\UserUart.h)(0x684A9BB1)
F (..\code\UserKeyMatrix.c)(0x688339C0)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\userkeymatrix.o --omf_browse .\objects\userkeymatrix.crf --depend .\objects\userkeymatrix.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\code\UserKeyMatrix.h)(0x688323C5)
I (..\..\components\driver\include\driver_gpio.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\code\UserKeyAdv.h)(0x68776514)
I (..\code\UserPrintf.h)(0x684A9BB1)
I (..\..\components\driver\include\driver_pmu.h)(0x684A9BB8)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\driver\include\driver_pmu_regs.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_wdt.h)(0x684A9BB8)
I (..\..\components\modules\os\include\os_timer.h)(0x684A9BB6)
I (..\..\components\modules\sys\include\sys_utils.h)(0x684A9BB6)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
F (..\..\components\ble\profiles\ble_user_profile\User_Deviceinfo_Service.c)(0x684A9BB2)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\user_deviceinfo_service.o --omf_browse .\objects\user_deviceinfo_service.crf --depend .\objects\user_deviceinfo_service.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\components\ble\include\gap\gap_api.h)(0x684A9BB1)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\ble\include\gatt\gatt_api.h)(0x684A9BB1)
I (..\..\components\modules\os\include\os_msg_q.h)(0x684A9BB6)
I (..\..\components\ble\include\gatt\gatt_sig_uuid.h)(0x684A9BB1)
I (..\..\components\modules\sys\include\sys_utils.h)(0x684A9BB6)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\code\UserPrintf.h)(0x684A9BB1)
I (..\code\UserAppConfig.h)(0x68564626)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\ble\profiles\ble_user_profile\User_Deviceinfo_Service.h)(0x684A9BB2)
F (..\..\components\ble\profiles\ble_user_profile\User_Spple_Service.c)(0x68555E36)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\user_spple_service.o --omf_browse .\objects\user_spple_service.crf --depend .\objects\user_spple_service.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\..\components\ble\include\gap\gap_api.h)(0x684A9BB1)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\ble\include\gatt\gatt_api.h)(0x684A9BB1)
I (..\..\components\modules\os\include\os_msg_q.h)(0x684A9BB6)
I (..\..\components\ble\include\gatt\gatt_sig_uuid.h)(0x684A9BB1)
I (..\..\components\ble\profiles\ble_user_profile\User_Spple_Service.h)(0x684A9BB2)
I (..\code\UserApp.h)(0x685566C8)
I (..\code\UserUart.h)(0x684A9BB1)
I (..\code\UserPrintf.h)(0x684A9BB1)
F (..\..\components\driver\driver_adc.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_adc.o --omf_browse .\objects\driver_adc.crf --depend .\objects\driver_adc.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\modules\sys\include\sys_utils.h)(0x684A9BB6)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_adc.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_flash.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_efuse.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu.h)(0x684A9BB8)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu_regs.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
F (..\..\components\driver\driver_codec.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_codec.o --omf_browse .\objects\driver_codec.crf --depend .\objects\driver_codec.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\driver\include\driver_codec.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
F (..\..\components\driver\driver_efuse.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_efuse.o --omf_browse .\objects\driver_efuse.crf --depend .\objects\driver_efuse.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\components\driver\include\driver_efuse.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
F (..\..\components\driver\driver_exti.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_exti.o --omf_browse .\objects\driver_exti.crf --depend .\objects\driver_exti.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\driver\include\driver_exti.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
F (..\..\components\driver\driver_i2s.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_i2s.o --omf_browse .\objects\driver_i2s.crf --depend .\objects\driver_i2s.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\driver\include\driver_i2s.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_uart.h)(0x684A9BB8)
F (..\..\components\driver\driver_iic.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_iic.o --omf_browse .\objects\driver_iic.crf --depend .\objects\driver_iic.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\driver\include\driver_iic.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\modules\sys\include\sys_utils.h)(0x684A9BB6)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
F (..\..\components\driver\driver_keyscan.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_keyscan.o --omf_browse .\objects\driver_keyscan.crf --depend .\objects\driver_keyscan.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\modules\common\include\co_log.h)(0x684A9BB8)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_keyscan.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu_regs.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
F (..\..\components\driver\driver_pdm.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_pdm.o --omf_browse .\objects\driver_pdm.crf --depend .\objects\driver_pdm.d)
I (..\..\components\driver\include\driver_pdm.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
F (..\..\components\driver\driver_pmu.c)(0x68555A69)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_pmu.o --omf_browse .\objects\driver_pmu.crf --depend .\objects\driver_pmu.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\jump_table.h)(0x684A9BB6)
I (..\..\components\modules\sys\include\sys_utils.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_pmu.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu_regs.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_adc.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_efuse.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_flash.h)(0x684A9BB8)
F (..\..\components\driver\driver_pmu_pwm.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_pmu_pwm.o --omf_browse .\objects\driver_pmu_pwm.crf --depend .\objects\driver_pmu_pwm.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu.h)(0x684A9BB8)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_pmu_regs.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_gpio.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_pmu_pwm.h)(0x684A9BB8)
F (..\..\components\driver\driver_pmu_qdec.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_pmu_qdec.o --omf_browse .\objects\driver_pmu_qdec.crf --depend .\objects\driver_pmu_qdec.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu_qdec.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\driver\include\driver_pmu.h)(0x684A9BB8)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu_regs.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
F (..\..\components\driver\driver_pwm.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_pwm.o --omf_browse .\objects\driver_pwm.crf --depend .\objects\driver_pwm.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pwm.h)(0x684A9BB8)
F (..\..\components\driver\driver_rtc.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_rtc.o --omf_browse .\objects\driver_rtc.crf --depend .\objects\driver_rtc.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\..\components\modules\sys\include\sys_utils.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_rtc.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_pmu.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu_regs.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
F (..\..\components\driver\driver_ssp.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_ssp.o --omf_browse .\objects\driver_ssp.crf --depend .\objects\driver_ssp.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\driver\include\driver_ssp.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_gpio.h)(0x684A9BB8)
F (..\..\components\driver\driver_system.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_system.o --omf_browse .\objects\driver_system.crf --depend .\objects\driver_system.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
F (..\..\components\driver\driver_timer.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_timer.o --omf_browse .\objects\driver_timer.crf --depend .\objects\driver_timer.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_system.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_timer.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
F (..\..\components\driver\driver_uart.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_uart.o --omf_browse .\objects\driver_uart.crf --depend .\objects\driver_uart.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\driver\include\driver_plf.h)(0x684A9BB8)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\core_cm3.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_compiler.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\cmsis_armcc.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\ll.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_uart.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
F (..\..\components\driver\driver_wdt.c)(0x684A9BB8)(--c99 -c --cpu Cortex-M3 -D__MICROLIB --li -g -O3 --apcs=interwork --split_sections -I ..\..\components\ble\include -I ..\..\components\driver\include -I ..\..\components\modules\os\include -I ..\..\components\modules\sys\include -I ..\..\components\modules\platform\include -I ..\..\components\modules\common\include -I ..\..\components\modules\lowpow\include -I ..\..\components\modules\button -I ..\..\components\ble\include\gap -I ..\..\components\ble\include\gatt -I ..\..\components\ble\profiles\ble_ota -I ..\code -I ..\..\components\ble\profiles\ble_user_profile -I ..\..\iot_ble_sdk\components\FlashDB\port\fal\inc -I ..\..\iot_ble_sdk\components\FlashDB\inc -I ..\..\iot_ble_sdk\components\FlashDB\wlt_kv -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\ringbuffer -I ..\..\iot_ble_sdk\components\util -I ..\..\iot_ble_sdk\components\ymodem

-IC:\Keil_v5\ARM\RV31\INC

-D__UVISION_VERSION="542"

-o .\objects\driver_wdt.o --omf_browse .\objects\driver_wdt.crf --depend .\objects\driver_wdt.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\components\modules\common\include\co_log.h)(0x684A9BB8)
I (..\..\components\modules\common\include\co_printf.h)(0x684A9BB8)
I (..\..\components\modules\sys\include\sys_utils.h)(0x684A9BB6)
I (..\..\components\modules\platform\include\compiler.h)(0x684A9BB6)
I (..\..\components\driver\include\driver_wdt.h)(0x684A9BB8)
I (..\..\components\modules\common\include\co_math.h)(0x684A9BB8)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\components\driver\include\driver_pmu.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_iomux.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_pmu_regs.h)(0x684A9BB8)
I (..\..\components\driver\include\driver_frspim.h)(0x684A9BB8)
F (..\..\components\modules\platform\source\app_boot_vectors.s)(0x684A9BB6)(--cpu Cortex-M3 --li -g --apcs=interwork --pd "__MICROLIB SETA 1"

-IC:\Keil_v5\ARM\RV31\INC

--pd "__UVISION_VERSION SETA 542"

--list .\listings\app_boot_vectors.lst --xref -o .\objects\app_boot_vectors.o --depend .\objects\app_boot_vectors.d)
F (..\..\Lib\WLT8016M.lib)(0x6882F457)()
