# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **WLT8016M BLE Remote Control** project (HW_PYP_Remote_H01-1) - an embedded firmware project for a Bluetooth Low Energy (BLE) remote control device with infrared (IR) functionality. The project targets the FR8016H ARM Cortex-M3 microcontroller.

## Build System

### GCC Build (Primary)
```bash
# Build the project
cd Project/gcc
make

# Clean build artifacts
make clean

# Build output location
# Generated files: Project/gcc/build/
```

### Keil Build (Alternative)
- Open `Project/keil/ble_multi_role.uvproj` in Keil µVision
- Build targets: `HW_PYP_Remote_H01-01` or `HW_Remote_H01-11`
- Output binaries are generated in `Project/keil/Output/`
- **Library Dependencies**:
  - GCC: Uses `libfr8010h_stack.a` and `syscall_gcc.txt`
  - Keil: Uses `fr8010h_stack.lib` and `syscall.txt`

## Architecture Overview

### Core Components
- **BLE Stack**: Multi-role BLE functionality using FR8016H stack library
- **IR System**: Infrared transmit/receive with learning capabilities
- **Flash Storage**: Customer parameter storage and FlashDB embedded database
- **Key Matrix**: Button handling and advertisement key processing
- **Power Management**: Low power modes and wakeup handling

### Directory Structure
- `Project/code/` - Main application code (User* files)
- `components/driver/` - Hardware driver layer
- `components/ble/` - BLE stack and profiles
- `components/modules/` - System modules (FreeRTOS, etc.)
- `iot_ble_sdk/components/` - IoT SDK components (FlashDB, easylogger)

### Key User Modules
- **UserCustomer.c**: Flash parameter management (MAC, Remote IDs)
- **UserKeyMatrix.c**: Key matrix scanning and processing
- **UserKeyAdv.c**: Advertisement key processing
- **UserBLE.h**: BLE connection and communication APIs
- **UserApp.h**: Core application interface definitions

## Hardware Configuration

### Target MCU
- **Chip**: FR8016H (ARM Cortex-M3)
- **Flash**: WLT8016M integrated flash memory
- **Clock**: Optimized for low power operation

### IR Hardware (红外硬件配置)
- **PC5**: IR transmission control (需要PWM功能的GPIO)
- **PC6**: IR learning input (需要中断功能的GPIO)
- **PC7**: IR learning enable/disable control (普通GPIO)

## Development Guidelines

### Flash Memory Layout
- Customer parameters stored at `USER_CUSTOMER_PARAMETER_ADDRESS` (0x7D000)
- FlashDB database for persistent storage with FAL (Flash Abstraction Layer)
- Use `flash_usage_config.h` for memory allocation
- **Memory Map (4M Flash)**:
  - IMAGE_SIZE: 0x28000 (160K each for imageA/imageB)
  - USER_PARAMETER_ADDRESS: 0x7B000 (8K user parameters)
  - USER_MAC_PAGE_ADDR: 0x7A000 (MAC address storage)
  - BLE_BONDING_INFO_SAVE_ADDR: 0x7D000 (BLE bonding info)

### BLE Development
- Multi-role support: Central, Peripheral, Observer, Broadcaster
- Custom service UUIDs configurable via UserApp APIs
- Connection parameter optimization for power efficiency

### IR Development
- Refer to `components/driver/IR/如何使用红外的驱动.txt` for implementation
- PWM-based transmission with precise timing (1.68ms = 1, 560us = 0)
- Learning mode with interrupt-driven capture

### Power Management
- Deep sleep wake-up detection via RTC alarm value (0x4C)
- Key-based wake-up handling in `User_CustomerInit()`
- Automatic connection resume after wake-up

## Common Tasks

### Adding New Features
1. Follow existing `User*` module patterns
2. Add function declarations to appropriate headers
3. Implement flash storage if parameters need persistence
4. Update build system (Makefile) if new source files added

### Debugging
- Enable debug output via `Display_Debug()` macro
- UART console at 115200 baud for debug messages
- Logic analyzer on PC5 for IR transmission debugging

### Testing IR Functionality
- Connect logic analyzer to PC5 pin
- Send test data (e.g., 0xBB) to verify PWM timing
- Check 38kHz carrier frequency and data encoding

## Important Notes

- This is a **defensive embedded system** - do not modify security-critical flash operations
- Chinese documentation available for IR driver usage
- FlashDB provides both Key-Value and Time Series database modes with ultra-lightweight footprint
- FreeRTOS integration for real-time task management
- Support for both GCC and Keil toolchains
- Power-off protection and wear leveling for flash longevity
- FAL (Flash Abstraction Layer) for unified flash operations