/*
 * ===============================================================================
 * LED闪烁控制功能说明
 * ===============================================================================
 *
 * 新版本使用独立定时器控制LED闪烁，具有以下特性：
 *
 * 1. 独立定时器控制：
 *    - 使用专门的os_timer_t g_led_blink_timer
 *    - 300ms切换间隔（灭300ms -> 亮300ms = 一次完整闪烁600ms）
 *    - 与睡眠管理定时器完全分离
 *    - 从常亮状态开始，先关闭LED，然后进行闪烁
 *
 * 2. 自动休眠保护：
 *    - 闪烁期间自动清除休眠计数器（AdvUserSleepNum = 0）
 *    - 确保LED闪烁过程不会被睡眠中断
 *    - 闪烁完成后恢复正常睡眠管理
 *
 * 3. 状态管理：
 *    - g_led_total_blinks: 总闪烁次数
 *    - g_led_remaining_blinks: 剩余闪烁次数
 *    - g_led_current_state: 当前LED状态（1=亮，0=灭）
 *
 * 4. 闪烁逻辑：
 *    - 正常状态：LED常亮
 *    - 开始闪烁：立即关闭LED（从常亮 -> 灭）
 *    - 闪烁过程：灭300ms -> 亮300ms（一次完整闪烁）
 *    - 结束闪烁：恢复常亮状态
 *
 * 5. 公共接口：
 *    - User_Start_LED_Blinking(count): 启动LED闪烁
 *    - User_Stop_LED_Blinking(): 停止LED闪烁
 *    - User_Is_LED_Blinking(): 检查是否正在闪烁
 *
 * 6. 使用示例：
 *    // 闪烁3次指示配对成功
 *    User_Start_LED_Blinking(3);
 *
 *    // 检查是否正在闪烁
 *    if (User_Is_LED_Blinking()) {
 *        // 正在闪烁，避免进入睡眠
 *    }
 *
 *    // 强制停止闪烁
 *    User_Stop_LED_Blinking();
 *
 * ===============================================================================
 */

#include <stdint.h>
#include <string.h>

#include "gap_api.h"
#include "gatt_api.h"

#include "../../components/modules/os/include/os_timer.h"
#include "button.h"
#include "jump_table.h"
#include "os_mem.h"
#include "sys_utils.h"

#include "UserApp.h"
#include "UserBLE.h"
#include "UserCustomer.h"
#include "UserKeyAdv.h"
#include "UserKeyMatrix.h"
#include "UserPrintf.h"
#include "driver_adc.h"
#include "driver_efuse.h"
#include "driver_flash.h"
#include "driver_gpio.h"
#include "driver_i2s.h"
#include "driver_iomux.h"
#include "driver_plf.h"
#include "driver_pmu.h"
#include "driver_rtc.h"
#include "driver_system.h"
#include "driver_uart.h"
#include "driver_wdt.h"
#include "flash_usage_config.h"

// For LED blinking control (using dedicated timer)
static uint8_t g_led_current_state = 1; // 1=ON, 0=OFF
// 新增独立的LED闪烁定时器
static os_timer_t g_led_blink_timer;
static uint8_t g_led_total_blinks = 0;     // 总闪烁次数（用于计算）
static uint8_t g_led_remaining_blinks = 0; // 剩余闪烁次数

// 调试输出
#define Display_Debug(_x) \
    do                    \
    {                     \
        co_printf _x;     \
    } while (0)

/*********************************************** LED & System Control
 * *******************************************/
#define BACKLED_PORT GPIO_PORT_D
#define BACKLED_BIT GPIO_BIT_7

#define BACKLED_ON() gpio_set_pin_value(BACKLED_PORT, BACKLED_BIT, 0)
#define BACKLED_OFF() gpio_set_pin_value(BACKLED_PORT, BACKLED_BIT, 1)

// 主控响应数据结构体
struct master_response_t
{
    uint8_t head;
    uint8_t cmd;
    uint8_t sum;
    uint8_t tail;
};

// 按键数据结构体
struct key_t
{
    unsigned char key_sta;  // 按键状态
    unsigned short key_num; // 按键值
};

// 系统管理相关变量
unsigned char AdvUserSleepNum;

// 定时器对象
os_timer_t Process_Sleep_Timer;

// 简化的按键状态管理
static uint32_t g_current_key_mask = 0;

// 定时发送相关变量
static os_timer_t g_data_send_timer;
static uint8_t g_data_send_timer_active = 0;
static uint32_t g_send_counter = 0;                // 发送计数器，用于调试

static uint8_t g_battery_level = 0; // 电池电量等级，分3级：1，低电量，2，中电量，3，高电量

extern unsigned char User_Ble_Send_EN;

static uint8_t get_battery_level(void);

// 定时发送函数前置声明
static void start_data_send_timer(void);
static void stop_data_send_timer(void);

/**
 * @brief 发送简化的12字节按键数据帧
 * @param keySta 按键状态结构体
 * @param mac MAC地址指针
 */
uint8_t key_test_buf[12] = {0};
uint8_t key_test_buf_len = 0;

static void send_key_data(struct key_t keySta, const uint8_t *mac)
{
    static uint32_t data_prep_counter = 0; // 数据准备计数器
    
    // 获取按键计数值
    extern uint8_t user_key_get_press_counter(void);
    uint8_t press_counter = user_key_get_press_counter();
    
    data_prep_counter++;
    Display_Debug(("=== DATA PREP #%d ===\r\n", data_prep_counter));
    
    // 12字节帧格式（保持原有格式）
    key_test_buf[0] = 0xAA;           // 帧头
    memcpy(key_test_buf + 1, mac, 6); // MAC地址

    // 按键值：按下时A5A5，释放时0000
    if (keySta.key_num != 0) {
        // 按键按下
        key_test_buf[7] = 0xA5;           // A5A5高字节
        key_test_buf[8] = 0xA5;           // A5A5低字节
    } else {
        // 按键释放
        key_test_buf[7] = 0x00;           // 0000高字节
        key_test_buf[8] = 0x00;           // 0000低字节
    }
    // 按键计数值 (1字节)
    key_test_buf[9] = press_counter;
    // 电池电量等级 (1字节)
    key_test_buf[10] = g_battery_level;

    // 计算校验和
    uint8_t checksum = 0;
    for (uint8_t i = 0; i < 11; i++)
    {
        checksum += key_test_buf[i];
    }

    key_test_buf[11] = checksum;
    key_test_buf_len = 12;

    Display_Debug(("Key data: %s + Counter:%d + Battery:%d\r\n", 
                   (keySta.key_num != 0) ? "A5A5" : "0000", 
                   press_counter, g_battery_level));
    Display_Debug(("BLE Send Status: %d\r\n", User_Ble_Send_EN));

    // 立即尝试发送
    send_key_data_delay();
}

/**
 * @brief 优化的发送按键数据（增加重复检查）
 */
void send_key_data_delay(void)
{
    static uint32_t transmission_counter = 0; // 发送计数器
    
    if (key_test_buf_len == 12)
    {
        if (User_Ble_Send_EN)
        {
            transmission_counter++;
            // BLE就绪，立即发送
            Display_Debug(
                ("=== TRANSMISSION #%d ===\r\n", transmission_counter));
            Display_Debug(
                ("Sending key data via BLE: %02X%02X + Counter:%d + Battery:%d, checksum=0x%02X\r\n",
                 key_test_buf[7], key_test_buf[8], key_test_buf[9], key_test_buf[10], key_test_buf[11]));
            
            // 添加详细的原始数据输出用于调试
            Display_Debug(("Raw 12-byte frame: "));
            for (int i = 0; i < 12; i++) {
                Display_Debug(("%02X ", key_test_buf[i]));
            }
            Display_Debug(("\r\n"));
            
            int send_result = user_spple_writenors_data(User_Get_Conn_Libconidx(1), key_test_buf, 12,
                                      User_Get_Conn_AttIndex(1));
            
            Display_Debug(("BLE send result: %d\r\n", send_result));
            key_test_buf_len = 0; // 清除缓冲区长度，防止重复发送
            Display_Debug(("Key data sent successfully, buffer cleared\r\n"));
            Display_Debug(("=== TRANSMISSION #%d COMPLETE ===\r\n", transmission_counter));
        }
        else
        {
            // BLE未就绪，暂存数据等待连接
            Display_Debug(("BLE not ready, data prepared for sending (Counter:%d, Battery:%d)\r\n", 
                          key_test_buf[9], key_test_buf[10]));
        }
    }
    else
    {
        // 缓冲区为空或无效
        Display_Debug(("No valid data to send (buf_len=%d)\r\n", key_test_buf_len));
    }
}

/**
 * @brief 检查是否有按键按下
 */
static unsigned char key_state_has_key_pressed(void)
{
    return (g_current_key_mask != 0) ? 1 : 0;
}

/**
 * @brief 定时发送按键数据的定时器处理函数
 * @param arg 定时器参数（未使用）
 */
static void data_send_timer_handler(void *arg)
{
    (void)arg;
    
    // 获取按键计数和发送标志
    extern uint8_t user_key_get_press_counter(void);
    extern uint8_t user_key_need_send(void);
    extern void user_key_clear_send_flag(void);
    
    uint8_t press_counter = user_key_get_press_counter();
    
    // 检查是否需要发送数据
    if (user_key_need_send())
    {
        // 检查BLE连接状态，如果未连接则重新连接
        if (!User_Ble_Send_EN)
        {
            Display_Debug(("Need send but BLE not connected, reconnecting...\r\n"));
            User_Conn_Start_Connect();
            return;  // 等待下次检查
        }
        
        mac_addr_t mac_addr;
        gap_address_get(&mac_addr);
        
        // 准备按键数据（按下状态）
        struct key_t key_data = {.key_num = 0xA5A5, .key_sta = 1};
        
        g_send_counter++;
        Display_Debug(("=== SEND #%d ===\r\n", g_send_counter));
        Display_Debug(("Sending: counter=%d\r\n", press_counter));
        
        // 发送按键数据
        send_key_data(key_data, mac_addr.addr);
        
        // 清除发送标志
        user_key_clear_send_flag();
        
        // 重置睡眠计数
        AdvUserSleepNum = 0;
    }
    
    // 定期检查BLE连接状态
    static uint32_t check_counter = 0;
    check_counter++;
    if (check_counter % 100 == 1)  // 每1秒检查一次
    {
        if (User_Ble_Send_EN)
        {
            Display_Debug(("BLE connected OK (counter=%d)\r\n", press_counter));
        }
        else
        {
            Display_Debug(("BLE not connected (counter=%d)\r\n", press_counter));
        }
    }
}

/**
 * @brief 启动定时发送定时器
 */
static void start_data_send_timer(void)
{
    if (!g_data_send_timer_active)
    {
        os_timer_init(&g_data_send_timer, data_send_timer_handler, NULL);
        os_timer_start(&g_data_send_timer, 10, 1); // 10ms间隔，周期性
        g_data_send_timer_active = 1;
        Display_Debug(("Data send timer started (10ms interval)\r\n"));
    }
}

/**
 * @brief 停止定时发送定时器
 */
static void stop_data_send_timer(void)
{
    if (g_data_send_timer_active)
    {
        os_timer_stop(&g_data_send_timer);
        g_data_send_timer_active = 0;
        Display_Debug(("Data send timer stopped\r\n"));
    }
}

/**
 * @brief 简化的单键遥控器事件回调函数（不再直接发送数据）
 * @param key_info 按键信息
 */
void matrix_key_event_callback(key_info_t *key_info)
{
    // 这个回调现在主要用于启动定时发送
    // 实际的按键检测和计数在key_process_and_send中处理
    
    Display_Debug(("Key callback: 0x%08X\r\n", key_info->key_mask));
    
    // 如果BLE已连接且定时器未启动，启动定时器
    if (User_Ble_Send_EN && !g_data_send_timer_active)
    {
        start_data_send_timer();
    }

    // 按键活动重置睡眠计数
    AdvUserSleepNum = 0;
}

/**
 * @brief LED闪烁定时器处理函数
 * @param arg 定时器参数（未使用）
 */
static void led_blink_timer_handler(void *arg)
{
    // 在闪烁期间始终清除休眠计数器，防止进入睡眠
    AdvUserSleepNum = 0;

    wdt_feed();

    if (g_led_remaining_blinks > 0)
    {
        // 切换LED状态
        if (g_led_current_state == 0)
        { // 当前灭，切换到亮
            BACKLED_ON();
            g_led_current_state = 1;
            g_led_remaining_blinks--; // 完成一次闪烁
            Display_Debug(("LED ON (blink %d/%d completed)\r\n",
                           g_led_total_blinks - g_led_remaining_blinks,
                           g_led_total_blinks));
        }
        else
        { // 当前亮，切换到灭
            BACKLED_OFF();
            g_led_current_state = 0;
            Display_Debug(("LED OFF (blink %d/%d)\r\n",
                           g_led_total_blinks - g_led_remaining_blinks + 1,
                           g_led_total_blinks));
        }

        // 如果还有剩余闪烁，继续定时器
        if (g_led_remaining_blinks > 0 || g_led_current_state == 0)
        {
            // 继续运行定时器（下次切换状态或完成当前闪烁）
            return;
        }
    }

    // 闪烁完成，停止定时器并恢复默认状态
    os_timer_stop(&g_led_blink_timer);
    BACKLED_ON(); // 恢复默认亮状态
    g_led_current_state = 1;
    g_led_remaining_blinks = 0;
    g_led_total_blinks = 0;

    // 重新启用组合动作检测
    extern void user_key_combo_detection_enable(uint8_t enable);
    user_key_combo_detection_enable(1);

    Display_Debug(("LED blinking completed, restored to default ON state\r\n"));
}

/**
 * @brief 启动LED闪烁（新版本 - 使用独立定时器）
 * @param count 闪烁次数
 */
static void start_led_blinking(uint8_t count)
{
    wdt_feed();

    Display_Debug(("Starting LED blinking: %d times\r\n", count));

    if (count == 0)
    {
        return;
    }

    // 确保LED闪烁定时器已初始化
    static uint8_t led_timer_initialized = 0;
    if (!led_timer_initialized) {
        // 初始化LED GPIO
        system_set_port_mux(BACKLED_PORT, BACKLED_BIT, PORTD7_FUNC_D7);
        gpio_set_dir(BACKLED_PORT, BACKLED_BIT, GPIO_DIR_OUT);
        BACKLED_ON(); // 设置默认状态为亮
        
        // 初始化LED闪烁定时器
        os_timer_init(&g_led_blink_timer, led_blink_timer_handler, NULL);
        led_timer_initialized = 1;
        Display_Debug(("LED GPIO and blink timer initialized on demand\r\n"));
    }

    // 停止当前闪烁（如果正在进行）
    os_timer_stop(&g_led_blink_timer);

    // 设置闪烁参数
    g_led_total_blinks = count;
    g_led_remaining_blinks = count;
    g_led_current_state = 1; // LED当前为亮（默认常亮状态）

    // 立即开始第一次闪烁：从常亮状态切换到灭
    BACKLED_OFF();
    g_led_current_state = 0;

    Display_Debug(("LED OFF (starting blink 1/%d) - from constant ON state\r\n", count));

    // 启动定时器，300ms间隔（灭300ms -> 亮300ms = 一次完整闪烁600ms）
    os_timer_start(&g_led_blink_timer, 300, 1); // 300ms周期，重复模式
}

/**
 * @brief 检查LED是否正在闪烁
 * @return 1-正在闪烁，0-未闪烁
 */
static uint8_t is_led_blinking(void)
{
    return (g_led_remaining_blinks > 0) ? 1 : 0;
}

// ==================== 公共接口函数 ====================

/**
 * @brief 启动LED闪烁（公共接口）
 * @param count 闪烁次数
 */
void User_Start_LED_Blinking(uint8_t count)
{
    start_led_blinking(count);
}

/**
 * @brief 停止LED闪烁（公共接口）
 */
void User_Stop_LED_Blinking(void)
{
    // 停止定时器
    os_timer_stop(&g_led_blink_timer);

    // 重置状态变量
    g_led_remaining_blinks = 0;
    g_led_total_blinks = 0;
    g_led_current_state = 1;

    // 恢复LED默认状态（亮）
    BACKLED_ON();

    Display_Debug(("LED blinking stopped by user, restored to default ON state\r\n"));
}

/**
 * @brief 检查LED是否正在闪烁（公共接口）
 * @return 1-正在闪烁，0-未闪烁
 */
uint8_t User_Is_LED_Blinking(void)
{
    return is_led_blinking();
}

/**
 * @brief 睡眠处理定时器（移除LED闪烁功能，专注于睡眠管理）
 */
__attribute__((section("ram_code"))) uint8_t app_get_ota_state(void);
static void User_Adv_Sleep_Process_Timer(void *arg)
{
    AdvUserSleepNum++;
    wdt_feed();

    if (app_get_ota_state())
    {
        wdt_stop();
        return;
    }

    g_battery_level = get_battery_level();

    // 检查LED是否正在闪烁，如果是则不进行睡眠处理
    if (is_led_blinking())
    {
        Display_Debug(("LED blinking active, sleep management suspended\r\n"));
        return; // LED闪烁期间暂停睡眠管理
    }

    // 检查是否有待发送的数据，如果BLE连接成功则立即发送
    if (User_Ble_Send_EN && key_test_buf_len == 12)
    {
        Display_Debug(("BLE connected! Sending pending data...\r\n"));
        send_key_data_delay();
    }
    
    // BLE连接成功，启动定时发送定时器
    if (User_Ble_Send_EN && !g_data_send_timer_active)
    {
        start_data_send_timer();
    }

    // 实时检查硬件按键状态，而不是依赖缓存状态
    uint32_t current_hardware_key_mask = user_key_get_current_hardware_state();

    // 如果检测到任何按键活动，重置睡眠计数
    if (current_hardware_key_mask != 0 || g_current_key_mask != 0)
    {
        Display_Debug(
            ("Keys active (hw=0x%08X, cache=0x%08X), resetting sleep countdown\r\n",
             current_hardware_key_mask, g_current_key_mask));
        
        // 如果硬件检测无按键但缓存有按键，强制清除缓存
        if (current_hardware_key_mask == 0 && g_current_key_mask != 0)
        {
            Display_Debug(("Force clearing stuck key cache\r\n"));
            g_current_key_mask = 0;
        }
        else
        {
            // 如果硬件检测到按键，立即触发一次按键扫描
            if (current_hardware_key_mask != 0)
            {
                Display_Debug(("Hardware detected key, triggering manual scan\r\n"));
                extern int user_key_scan(void);
                user_key_scan();
            }
            AdvUserSleepNum = 0;
            return;
        }
    }

    // 每10秒输出一次睡眠倒计时状态
    if (AdvUserSleepNum % 100 == 50)
    {
        Display_Debug(("Sleep countdown: %d/29 (hw=0x%08X, cache=0x%08X)\r\n", 
                      AdvUserSleepNum, current_hardware_key_mask, g_current_key_mask));
    }

    // 每次都输出详细的睡眠状态
    Display_Debug(("Sleep countdown: %d/29 (hw=0x%08X, cache=0x%08X, LED=%d)\r\n", 
                  AdvUserSleepNum, current_hardware_key_mask, g_current_key_mask, User_Is_LED_Blinking()));

    // 检查是否有其他阻止休眠的因素
    extern uint8_t User_Is_LED_Blinking(void);
    if (User_Is_LED_Blinking())
    {
        Display_Debug(("Sleep blocked by LED blinking\r\n"));
        return;
    }

    // 第一阶段：达到2.7秒时断开BLE连接
    if (AdvUserSleepNum == 29)
    {
        // 最后一次检查：实时扫描硬件按键状态
        uint32_t final_check_mask = user_key_get_current_hardware_state();
        if (final_check_mask != 0)
        {
            Display_Debug(
                ("Sleep blocked - keys detected in final check (mask=0x%08X)\r\n",
                 final_check_mask));
            AdvUserSleepNum = 0; // 重置为0，给更多时间
            return;
        }

        // 检查缓存状态
        if (key_state_has_key_pressed())
        {
            Display_Debug(("Sleep blocked - keys still pressed (mask=0x%08X)\r\n",
                           g_current_key_mask));
            AdvUserSleepNum = 0; // 重置为0，下次循环再检查
            return;
        }

        Display_Debug(("Disconnecting BLE before sleep...\r\n"));
        User_DisConnect_BLE(1);
        return; // 退出，等待下一次定时器中断
    }

    // 第二阶段：达到3秒时进入深度睡眠
    if (AdvUserSleepNum >= 30)
    {
        AdvUserSleepNum = 0;

        // 最后再次检查按键状态
        uint32_t final_check_mask = user_key_get_current_hardware_state();
        if (final_check_mask != 0 || key_state_has_key_pressed())
        {
            Display_Debug(("Sleep canceled - keys detected in final check\r\n"));
            AdvUserSleepNum = 0;
            return;
        }

        // 进入低功耗模式
        Display_Debug(("Attempting to enter low power mode\r\n"));
        int low_power_result = user_key_enter_low_power();
        if (low_power_result == -2)
        {
            Display_Debug(("Low power blocked - keys still pressed\r\n"));
            AdvUserSleepNum = 0;
            return;
        }
        else if (low_power_result != 0)
        {
            Display_Debug(
                ("Matrix key enter low power failed: %d\r\n", low_power_result));
        }

        // 设置RTC闹钟值
        ool_write32(PMU_REG_RTC_ALMA_VALUE_0, 0x4C);

        // 停止睡眠管理定时器
        os_timer_stop(&Process_Sleep_Timer);

        // 停止LED闪烁定时器（如果正在运行）
        os_timer_stop(&g_led_blink_timer);
        g_led_remaining_blinks = 0;
        g_led_total_blinks = 0;
        
        // 停止定时发送定时器
        stop_data_send_timer();

        // 强制停止按键扫描定时器
        extern int user_key_stop_scanning(void);
        user_key_stop_scanning();
        Display_Debug(("Forced key scanning stop before sleep\r\n"));

        // 按键计数器重置已在user_key_enter_low_power()中处理

        // 停止看门狗
        wdt_stop();

        // 配置唤醒源
        pmu_port_wakeup_func_set(GPIO_PA6);

        BACKLED_OFF();

        Display_Debug(("Entering deep sleep mode\r\n"));
        co_printf("sleep\r\n");

        // 进入深度睡眠
        system_sleep_enable();
        system_power_off(false);
    }
}

/**
 * @brief 电池ADC初始化
 */
static void Battery_ADC_Init(void)
{
    struct adc_cfg_t cfg;

    system_set_port_mux(GPIO_PORT_D, GPIO_BIT_5, PORTD5_FUNC_ADC1);

    memset((void *)&cfg, 0, sizeof(cfg));
    cfg.src = ADC_TRANS_SOURCE_PAD;
    cfg.ref_sel = ADC_REFERENCE_AVDD;
    cfg.channels = 0x01;
    cfg.route.pad_to_sample = 1;
    cfg.clk_sel = ADC_SAMPLE_CLK_24M_DIV13;
    cfg.clk_div = 0x3f;
    adc_init(&cfg);
    adc_enable(NULL, NULL, 0);
}

/**
 * @brief 获取电池电量等级
 *
 * @return uint8_t 电量等级
 */
static uint8_t get_battery_level(void)
{
    uint16_t result;

    // For 3.0V input:
    // -ADC input voltage = 3.0V × 0.4348 = 1.304V - ADC value = (1.304V / 3.3V) × 4095 = 1617
    // For 2.8V input : -ADC input voltage = 2.8V × 0.4348 = 1.217V - ADC value = (1.217V / 3.3V) × 4095 = 1508
    // For 2.6V input : -ADC input voltage = 2.6V × 0.4348 = 1.130V - ADC value = (1.130V / 3.3V) × 4095 = 1401

    // Results:
    // -3.0V → ADC value : 1617
    // - 2.8V → ADC value : 1508
    // - 2.6V → ADC value : 1401

    adc_get_result(ADC_TRANS_SOURCE_PAD, 0x01, &result);

    Display_Debug(("Battery ADC result: %d\r\n", result));

    if (result > 1500)
    {
        return 3;
    }
    else if (result > 1400 && result < 1500)
    {
        return 2;
    }
    else if (result < 1400)
    {
        return 1;
    }
	
	return 0;
}

/**
 * @brief 按键处理初始化（简化版）
 */
int User_Adv_Key_Process_Init(void)
{
    Display_Debug(("=== Key Process Init (Simplified) ===\r\n"));

    Battery_ADC_Init();

    // 初始化LED控制
    system_set_port_mux(BACKLED_PORT, BACKLED_BIT, PORTD7_FUNC_D7);
    gpio_set_dir(BACKLED_PORT, BACKLED_BIT, GPIO_DIR_OUT);

    BACKLED_ON();

    // 初始化矩阵按键模块
    key_config_t key_config = {.scan_interval = 3, // 修改为3ms扫描间隔，确保快速响应
                               .callback =
                                   (key_callback_t)matrix_key_event_callback};

    Display_Debug(("Initializing matrix key module...\r\n"));
    int key_init_result = user_key_init(&key_config);
    if (key_init_result == 0)
    {
        Display_Debug(("Matrix key module initialized successfully\r\n"));

        // 检查是否已经从唤醒启动了扫描，如果是则重新启动
        if (user_key_is_scanning_needed())
        {
            Display_Debug(("Restarting key scanning after init (wakeup detected)\r\n"));
            user_key_start_scanning();
        }
    }
    else
    {
        Display_Debug(
            ("Matrix key initialization failed: %d\r\n", key_init_result));
    }

    // 启动睡眠管理定时器
    Display_Debug(("Initializing sleep timer...\r\n"));
    os_timer_init(&Process_Sleep_Timer, User_Adv_Sleep_Process_Timer, NULL);
    os_timer_start(&Process_Sleep_Timer, 100, 1);
    Display_Debug(("Sleep timer started\r\n"));

    // 初始化LED闪烁定时器
    Display_Debug(("Initializing LED blink timer...\r\n"));
    os_timer_init(&g_led_blink_timer, led_blink_timer_handler, NULL);
    g_led_total_blinks = 0;
    g_led_remaining_blinks = 0;
    g_led_current_state = 1; // LED默认为亮
    Display_Debug(("LED blink timer initialized\r\n"));

    // 初始化系统变量
    AdvUserSleepNum = 0;
    g_current_key_mask = 0;

    // 初始化发送状态管理
    g_send_counter = 0;

    // 唤醒按键现在由状态机统一管理，不需要额外处理
    Display_Debug(("Wakeup key handled by state machine\r\n"));

    Display_Debug(("=== Key Process Init Complete ===\r\n"));
    return 0;
}

/************************************************* Scan
 * *********************************************/
int User_Rcv_Remote_Data_Process(unsigned char *data, unsigned char len)
{
    // 数据接收调试信息
    Display_Debug(("=== Remote Data Process Start ===\r\n"));
    Display_Debug(("Received data length: %d\r\n", len));

    // 打印接收到的原始数据
    Display_Debug(("Raw data: "));
    for (int i = 0; i < len && i < 16; i++)
    {
        Display_Debug(("0x%02X ", data[i]));
    }
    Display_Debug(("\r\n"));

    // 检查数据长度
    if (len < sizeof(struct master_response_t))
    {
        Display_Debug(("Data length too short, expected: %d, received: %d\r\n",
                       sizeof(struct master_response_t), len));
        return 0;
    }

    struct master_response_t *response = (struct master_response_t *)data;

    Display_Debug(("Frame structure - Head: 0x%02X, Cmd: 0x%02X, Sum: 0x%02X, "
                   "Tail: 0x%02X\r\n",
                   response->head, response->cmd, response->sum, response->tail));

    // 验证帧头
    if (response->head != 0x55)
    {
        Display_Debug(("Frame head error, expected: 0x55, received: 0x%02X\r\n",
                       response->head));
        return 0;
    }
    Display_Debug(("Frame head OK\r\n"));

    // 验证帧尾
    if (response->tail != 0xfe)
    {
        Display_Debug(("Frame tail error, expected: 0xFE, received: 0x%02X\r\n",
                       response->tail));
        return 0;
    }
    Display_Debug(("Frame tail OK\r\n"));

    // 验证校验和
    uint8_t sum = response->head + response->cmd;
    if (sum != response->sum)
    {
        Display_Debug(("Checksum error, calculated: 0x%02X, received: 0x%02X\r\n",
                       sum, response->sum));
        return 0;
    }
    Display_Debug(("Checksum OK\r\n"));

    // 处理命令
    Display_Debug(("Processing command: 0x%02X\r\n", response->cmd));

    // Reset sleep counter on any valid command
    AdvUserSleepNum = 0;

    if (response->cmd == 0x01)
    {
        User_Customer_Save_Default_Para(); // 存储配对MAC

        // 闪烁BACKLED 3次指示配对成功 (使用新的独立定时器)
        User_Start_LED_Blinking(3);
    }
    else if (response->cmd == 0x02)
    {
        User_Customer_Reset_SlaveMac(); // 清除配对MAC

        // 闪烁BACKLED 3次指示MAC地址已清除 (使用新的独立定时器)
        User_Start_LED_Blinking(3);
    }
    else
    {
        Display_Debug(("Unknown command: 0x%02X\r\n", response->cmd));
    }

    Display_Debug(("=== Remote Data Process End ===\r\n"));
    return 0;
}
