#ifndef _USER_KEYADV_H
#define _USER_KEYADV_H

#include "UserKeyMatrix.h"  // 包含key_info_t定义

// ==================== LED控制相关 ====================

/**
 * @brief 启动LED闪烁
 * @param count 闪烁次数
 * @note 使用独立定时器控制LED闪烁，闪烁期间会自动清除休眠计数器
 */
void User_Start_LED_Blinking(uint8_t count);

/**
 * @brief 停止LED闪烁
 * @note 立即停止LED闪烁并恢复默认亮状态
 */
void User_Stop_LED_Blinking(void);

/**
 * @brief 检查LED是否正在闪烁
 * @return 1-正在闪烁，0-未闪烁
 */
uint8_t User_Is_LED_Blinking(void);

// ==================== 系统管理相关 ====================

/**
 * @brief 按键处理初始化（简化版）
 * @return 0-成功
 * @note 使用矩阵按键模块，自动注册回调函数处理按键事件
 */
int User_Adv_Key_Process_Init(void);

/**
 * @brief 矩阵按键事件回调函数（简化版）
 * @param key_info 按键信息
 * @note 处理按键事件，直接发送到BLE
 */
void matrix_key_event_callback(key_info_t *key_info);

// ==================== 通信和数据传输 ====================

/**
 * @brief 处理接收到的对端数据
 * @param data 数据内容
 * @param len 数据长度
 * @return 0-成功
 */
int User_Rcv_Remote_Data_Process(unsigned char *data, unsigned char len);

/**
 * @brief 发送按键数据（简化版）
 */
void send_key_data_delay(void);



/**
 * @brief 启动连接
 */
int User_Conn_Start_Connect(void);

#endif
