#ifndef _USER_KEY_MATRIX_H
#define _USER_KEY_MATRIX_H

#include "driver_gpio.h"
#include "driver_iomux.h"
#include "driver_system.h"
#include <stdint.h>

// 按键扫描模式（仅支持PA6单键模式）
typedef enum
{
    KEY_SCAN_MODE_PA6 = 0 // PA6单键扫描模式
} key_scan_mode_t;

// 简化的按键事件类型
typedef enum
{
    KEY_EVENT_PRESS = 0 // 单一事件类型，直接发送当前键值
} key_event_t;

// 按键信息结构体（简化版）
typedef struct
{
    uint16_t key_mask; // 按键掩码（包括0值）
    key_event_t event; // 按键事件（固定为PRESS）
    uint16_t duration; // 保留字段，固定为0
} key_info_t;

// 按键回调函数类型
typedef void (*key_callback_t)(key_info_t *key_info);

// 按键配置结构体（简化版）
typedef struct
{
    uint16_t scan_interval;  // 扫描间隔(ms)
    key_callback_t callback; // 事件回调函数
} key_config_t;

// PA6单键配置
#define PA6_KEY_MAX_KEYS 1

// 引脚配置结构体（保留兼容性）
typedef struct
{
    enum system_port_t port;    // GPIO端口
    enum system_port_bit_t bit; // GPIO位
    uint32_t func;              // 引脚功能枚举
    uint32_t pull_pin;          // 上拉引脚定义枚举
} gpio_pin_config_t;

// PA6按键引脚配置结构体（简化版）
typedef struct
{
    gpio_pin_config_t pa6_key; // PA6引脚配置
} pa6_key_pin_config_t;

// 保留兼容性的旧结构体名称
typedef pa6_key_pin_config_t matrix_key_pin_config_t;

// ==================== 核心接口函数 ====================

/**
 * @brief 按键驱动初始化
 * @param config 配置参数
 * @return 0-成功，负数-错误码
 */
int user_key_init(key_config_t *config);

/**
 * @brief 启动按键扫描（BLE连接成功后调用）
 * @return 0-成功，负数-错误码
 */
int user_key_start_scanning(void);

/**
 * @brief 停止按键扫描
 * @return 0-成功
 */
int user_key_stop_scanning(void);

/**
 * @brief 手动触发按键扫描
 * @return 0-成功
 */
int user_key_scan(void);

/**
 * @brief 进入低功耗模式
 * @return 0-成功，-2-有按键按下拒绝进入
 */
int user_key_enter_low_power(void);

// ==================== 唤醒处理接口 ====================

/**
 * @brief PA6唤醒处理函数（单键检测逻辑）
 * @return 0-成功，负数-错误码
 */
int user_key_wakeup_handler(void);

// 单键遥控器不需要复杂的缓存机制

/**
 * @brief 检查是否有唤醒按键等待发送
 * @return 0-无等待按键，1-有按键等待发送
 */
int user_key_has_pending_wakeup(void);

/**
 * @brief 获取BLE连接状态
 * @return 0-未连接，1-已连接可发送
 */
int user_key_is_ble_connecting(void);

/**
 * @brief 获取当前PA6按键状态（实时扫描）
 * @return 当前按键掩码，0表示无按键按下，1表示PA6按键按下
 */
uint32_t user_key_get_current_hardware_state(void);

// ==================== 低功耗配置接口 ====================

/**
 * @brief PA6按键低功耗配置
 * @return 0-成功
 */
int user_key_matrix_low_power_config(void);

// ================== 状态查询函数 ==================

/**
 * @brief 检查是否需要重新启动按键扫描
 * @return 1-需要启动扫描，0-不需要
 */
int user_key_is_scanning_needed(void);

/**
 * @brief 检查是否有唤醒按键等待发送
 * @return 0-无等待按键，1-有按键等待发送
 */
int user_key_has_pending_wakeup(void);

// ================== 组合动作控制接口 ==================

/**
 * @brief 启用/禁用组合动作检测
 * @param enable 1-启用，0-禁用
 */
void user_key_combo_detection_enable(uint8_t enable);

/**
 * @brief 获取当前组合动作检测状态
 * @return 1-启用，0-禁用
 */
uint8_t user_key_combo_detection_status(void);

/**
 * @brief 获取当前按键计数值
 * @return 按键计数值
 */
uint8_t user_key_get_press_counter(void);

/**
 * @brief 清零按键计数值
 */
void user_key_reset_press_counter(void);

/**
 * @brief 检查是否需要发送按键数据
 * @return 1-需要发送，0-不需要
 */
uint8_t user_key_need_send(void);

/**
 * @brief 清除发送标志
 */
void user_key_clear_send_flag(void);

#endif /* _USER_KEY_MATRIX_H */
