/**
 * @file UserKeyMatrix.c
 * @brief PA6单键检测模块 - 简化版：仅使用PA6进行按键检测
 * <AUTHOR>
 * @date 2024-12-19
 * @version 4.0 - PA6单键检测版：移除矩阵扫描，仅保留PA6检测
 */

#include <stdint.h>
#include "UserKeyMatrix.h"
#include "UserKeyAdv.h"
#include "UserPrintf.h"
#include "driver_gpio.h"
#include "driver_iomux.h"
#include "driver_pmu.h"
#include "driver_system.h"
#include "driver_wdt.h"
#include "os_timer.h"
#include "sys_utils.h"

// 启用调试输出
#define Key_Matrix_Debug(_x) \
    do                       \
    {                        \
        co_printf _x;        \
    } while (0)

// PA6按键引脚配置（简化版）
// 使用PA6作为唯一的按键检测引脚
#define PA6_KEY_PRESSED_VALUE 0  // 按键按下时PA6的电平值
#define PA6_KEY_RELEASED_VALUE 1 // 按键释放时PA6的电平值

// 按键驱动状态结构体（简化版）
typedef struct
{
    key_config_t config;
    uint8_t initialized;
    uint8_t scan_active;
    uint8_t scan_enabled; // 扫描使能标志，用于控制是否允许扫描
} key_driver_t;

// 全局变量
static key_driver_t g_key_driver = {0};
static os_timer_t g_key_scan_timer;

// 唤醒相关变量
static uint8_t g_wakeup_recovery_needed = 0;

// 简化的单键遥控器不需要复杂的缓存机制

// 外部BLE连接标志位
extern unsigned char User_Ble_Send_EN;

// 按键状态管理
static uint32_t g_last_key_mask = 0;
static uint8_t g_key_press_counter = 0; // 按键次数计数器

// 组合动作状态管理
typedef enum
{
    COMBO_STATE_IDLE = 0,       // 空闲状态
    COMBO_STATE_FIRST_PRESS,    // 第一次按下
    COMBO_STATE_FIRST_RELEASE,  // 第一次释放
    COMBO_STATE_SECOND_PRESS,   // 第二次按下
    COMBO_STATE_SECOND_RELEASE, // 第二次释放
    COMBO_STATE_LONG_PRESS      // 长按状态
} combo_state_t;

static combo_state_t g_combo_state = COMBO_STATE_IDLE;
static uint32_t g_combo_timer = 0;           // 组合动作计时器
static uint32_t g_press_start_time = 0;      // 按键按下开始时间
static uint32_t g_release_start_time = 0;    // 按键释放开始时间
static uint8_t g_combo_detection_active = 1; // 组合动作检测开关

// 组合动作时间参数（单位：扫描周期，3ms一次）
#define COMBO_SHORT_PRESS_MAX_TIME 500    // 1500ms最大短按时间（500 × 3ms = 1500ms）
#define COMBO_PRESS_INTERVAL_MAX_TIME 667 // 2000ms最大按键间隔（667 × 3ms = 2001ms）
#define COMBO_LONG_PRESS_MIN_TIME 1000    // 3000ms最小长按时间（1000 × 3ms = 3000ms）
#define COMBO_TIMEOUT_MAX_TIME 5000       // 15000ms总超时时间（5000 × 3ms = 15000ms）

// 静态函数声明
static void key_scan_timer_handler(void *arg);
static void key_process_and_send(uint32_t key_mask);
static void key_gpio_init_pa6(void);
static uint32_t key_pa6_scan_hardware(void);
static void key_enter_low_power_mode(void);
static void key_gpio_ensure_pa6_state(void);
// 单键遥控器不需要复杂的超时处理
static void combo_action_reset(void);
static void combo_action_process(uint32_t key_mask);
static void combo_action_execute_mac_clear(void);

// 单键遥控器不需要复杂的缓存机制

/**
 * @brief 按键驱动初始化（改进版：不立即启动扫描）
 */
int user_key_init(key_config_t *config)
{
    if (config == NULL)
    {
        return -1;
    }

    if (g_key_driver.initialized)
    {
        Key_Matrix_Debug(("Key driver already initialized, updating config only\r\n"));
        // 如果已经初始化，只更新配置，保持当前扫描状态和按键状态
        g_key_driver.config = *config;
        return 0;
    }

    g_key_driver.config = *config;

    key_gpio_init_pa6();

    // 初始化定时器但不启动
    os_timer_init(&g_key_scan_timer, key_scan_timer_handler, NULL);

    // 单键遥控器不需要BLE连接超时定时器

    g_key_driver.initialized = 1;
    g_key_driver.scan_active = 0;  // 标记为未启动
    g_key_driver.scan_enabled = 0; // 默认禁用扫描
    g_last_key_mask = 0;

    // 初始化组合动作状态
    combo_action_reset();
    g_combo_detection_active = 1; // 默认启用组合动作检测

    Key_Matrix_Debug(
        ("Key matrix initialized, scanning disabled until BLE ready\r\n"));
    return 0;
}

/**
 * @brief 启动按键扫描（BLE连接成功后调用）
 */
int user_key_start_scanning(void)
{
    if (!g_key_driver.initialized)
    {
        Key_Matrix_Debug(("Key driver not initialized\r\n"));
        return -1;
    }

    if (g_key_driver.scan_active)
    {
        Key_Matrix_Debug(("Key scanning already active\r\n"));
        return 0;
    }

    // 单键遥控器不需要复杂的超时机制

    g_key_driver.scan_enabled = 1;
    g_key_driver.scan_active = 1;
    os_timer_start(&g_key_scan_timer, g_key_driver.config.scan_interval, 1); // 改为周期性定时器

    Key_Matrix_Debug(("Key scanning started after BLE connection\r\n"));
    return 0;
}

/**
 * @brief 停止按键扫描
 */
int user_key_stop_scanning(void)
{
    if (g_key_driver.scan_active)
    {
        os_timer_stop(&g_key_scan_timer);
        g_key_driver.scan_active = 0;
        g_key_driver.scan_enabled = 0;
        Key_Matrix_Debug(("Key scanning stopped\r\n"));
    }

    // 单键遥控器不需要复杂的超时机制

    return 0;
}

/**
 * @brief 按键扫描（手动调用）- 增加保护检查
 */
int user_key_scan(void)
{
    if (!g_key_driver.initialized)
    {
        return -1;
    }

    // 检查是否允许扫描（保护唤醒键值期间禁止扫描）
    if (!g_key_driver.scan_enabled)
    {
        Key_Matrix_Debug(("Key scan blocked - waiting for BLE connection\r\n"));
        return 0;
    }

    uint32_t key_mask = key_pa6_scan_hardware();
    Key_Matrix_Debug(("user_key_scan: key_mask=0x%08X, last_mask=0x%08X\r\n", key_mask, g_last_key_mask));
    key_process_and_send(key_mask);
    return 0;
}

/**
 * @brief 进入低功耗模式
 */
int user_key_enter_low_power(void)
{
    if (!g_key_driver.initialized)
    {
        return -1;
    }

    // 检查：如果当前PA6按键按下，拒绝进入低功耗模式
    uint32_t current_key_mask = key_pa6_scan_hardware();
    if (current_key_mask != 0)
    {
        Key_Matrix_Debug(
            ("Refuse to enter low power - PA6 key still pressed: 0x%08X\r\n",
             current_key_mask));
        return -2;
    }

    Key_Matrix_Debug(("Entering low power mode\r\n"));
    Key_Matrix_Debug(("Stopping key scanning before sleep\r\n"));
    user_key_stop_scanning(); // 停止扫描
    g_last_key_mask = 0;

    // 进入低功耗前清零按键计数器
    g_key_press_counter = 0;
    Key_Matrix_Debug(("Key press counter reset to 0 before sleep\r\n"));

    key_enter_low_power_mode();
    Key_Matrix_Debug(("Low power mode configured\r\n"));
    return 0;
}

/**
 * @brief PA6按键低功耗配置（简化版）
 */
int user_key_matrix_low_power_config(void)
{
    // 仅配置PA6为PMU管理，用于唤醒检测
    pmu_set_port_mux(GPIO_PORT_A, GPIO_BIT_6, PMU_PORT_MUX_GPIO);
    pmu_set_pin_to_PMU(GPIO_PORT_A, BIT(GPIO_BIT_6));
    pmu_set_pin_dir(GPIO_PORT_A, BIT(GPIO_BIT_6), GPIO_DIR_IN);
    pmu_set_pin_pull(GPIO_PORT_A, BIT(GPIO_BIT_6), true); // 上拉

    Key_Matrix_Debug(("PA6 low power config completed\r\n"));
    return 0;
}

// ======================== 静态函数实现 ========================

/**
 * @brief 按键扫描定时器处理函数
 */
static void key_scan_timer_handler(void *arg)
{
    (void)arg;

    static uint32_t scan_count = 0;
    scan_count++;

    // 每次都输出调试信息来诊断定时器问题
    Key_Matrix_Debug(("Timer scan #%d, enabled=%d, active=%d\r\n",
                      scan_count, g_key_driver.scan_enabled, g_key_driver.scan_active));

    // 检查扫描是否被禁用
    if (!g_key_driver.scan_enabled)
    {
        return;
    }

    user_key_scan();
}

/**
 * @brief 简化的按键处理和发送（单键遥控器版）
 * @param key_mask 当前扫描到的按键掩码
 */
// 简单的按键检测变量
static uint8_t g_need_send_flag = 0; // 需要发送标志

static void key_process_and_send(uint32_t key_mask)
{
    Key_Matrix_Debug(("key_process_and_send: mask=0x%08X, last=0x%08X, counter=%d, combo_active=%d\r\n",
                     key_mask, g_last_key_mask, g_key_press_counter, g_combo_detection_active));

    // 无论组合动作检测是否启用，都要处理基本的按键计数
    // 简单检测：每次从释放到按下就计数一次
    if (key_mask != 0 && g_last_key_mask == 0)
    {
        // 检测到按键按下
        g_key_press_counter++;
        if (g_key_press_counter == 0) // 防止溢出
        {
            g_key_press_counter = 1;
        }

        g_need_send_flag = 1; // 立即发送

        Key_Matrix_Debug(("=== BUTTON PRESS COUNT: %d === (COMBO DISABLED)\r\n",
                         g_key_press_counter));
    }
    else if (key_mask == 0 && g_last_key_mask != 0)
    {
        Key_Matrix_Debug(("Key released (counter=%d)\r\n", g_key_press_counter));
    }

    // 暂时屏蔽组合动作，专注测试按键计数
    // 处理组合动作检测（在按键计数之后）
    if (g_combo_detection_active && 0) // 暂时禁用组合动作
    {
        combo_action_process(key_mask);
    }

    // 更新上次状态
    g_last_key_mask = key_mask;
}

/**
 * @brief PA6按键GPIO初始化（简化版）
 */
static void key_gpio_init_pa6(void)
{
    // 仅配置PA6为输入上拉
    pmu_set_pin_to_CPU(GPIO_PORT_A, BIT(GPIO_BIT_6));
    for (volatile int j = 0; j < 100; j++)
        ;

    system_set_port_mux(GPIO_PORT_A, GPIO_BIT_6, PORTA6_FUNC_A6);
    gpio_set_dir(GPIO_PORT_A, GPIO_BIT_6, GPIO_DIR_IN);

    uint32_t gpio_mask = (1 << GPIO_BIT_6);
    system_set_port_pull(gpio_mask, 1);

    Key_Matrix_Debug(("PA6 GPIO initialized as input with pull-up\r\n"));
}

/**
 * @brief PA6按键GPIO状态确保函数（简化版）
 */
static void key_gpio_ensure_pa6_state(void)
{
    // 确保PA6为输入模式
    gpio_set_dir(GPIO_PORT_A, GPIO_BIT_6, GPIO_DIR_IN);
}

static uint32_t key_pa6_scan_hardware(void)
{
    uint32_t key_mask = 0;
    wdt_feed();

    if (g_wakeup_recovery_needed)
    {
        key_gpio_init_pa6();
        g_wakeup_recovery_needed = 0;
    }
    else
    {
        key_gpio_ensure_pa6_state();
    }

    // 检测PA6状态
    int pa6_value = gpio_get_pin_value(GPIO_PORT_A, GPIO_BIT_6);

    // 如果PA6为低电平，表示按键按下
    if (pa6_value == PA6_KEY_PRESSED_VALUE)
    {
        key_mask = 0xA5A5; // 位0表示PA6按键按下
    }

    // 简化的硬件扫描，只检测按键状态，不处理状态变化
    // 状态变化由上层的key_process_and_send处理
    static uint32_t debug_counter = 0;
    debug_counter++;

    if (key_mask != 0)
    {
        Key_Matrix_Debug(("PA6 hardware: PRESSED (value=%d) -> key_mask=0x%08X [scan#%d]\r\n",
                          pa6_value, key_mask, debug_counter));
    }
    else if (debug_counter % 100 == 1)
    {
        // 每300ms输出一次释放状态
        Key_Matrix_Debug(("PA6 hardware: RELEASED (value=%d) -> key_mask=0x%08X [scan#%d]\r\n",
                          pa6_value, key_mask, debug_counter));
    }

    // 如果有按键按下且BLE未连接，启动BLE连接
    if (key_mask != 0 && User_Ble_Send_EN == 0)
    {
        User_Conn_Start_Connect();
    }

    return key_mask;
}

/**
 * @brief 进入低功耗模式的GPIO配置
 */
static void key_enter_low_power_mode(void)
{
    user_key_matrix_low_power_config();
}

void user_key_wakeup_init(void)
{
    // 配置PA6为输入上拉模式，为后续扫描准备
    pmu_set_pin_to_CPU(GPIO_PORT_A, BIT(GPIO_BIT_6));
    system_set_port_mux(GPIO_PORT_A, GPIO_BIT_6, PORTA6_FUNC_A6);
    gpio_set_dir(GPIO_PORT_A, GPIO_BIT_6, GPIO_DIR_IN);
    uint32_t gpio_mask = (1 << GPIO_BIT_6);
    system_set_port_pull(gpio_mask, 1);
}

/**
 * @brief 简化的唤醒处理函数（单键遥控器版）
 * @return 0-成功，负数-错误码
 */
int user_key_wakeup_handler(void)
{
    Key_Matrix_Debug(("=== WAKEUP HANDLER START ===\r\n"));

    // 唤醒就算一次计数
    g_key_press_counter++;
    if (g_key_press_counter == 0) // 防止溢出
    {
        g_key_press_counter = 1;
    }

    g_need_send_flag = 1; // 标记需要发送

    Key_Matrix_Debug(("=== WAKEUP BUTTON PRESS COUNT: %d === (COMBO DISABLED)\r\n", g_key_press_counter));

    // 暂时屏蔽组合动作检测
    // 启动组合动作检测
    if (g_combo_detection_active && g_combo_state == COMBO_STATE_IDLE && 0) // 暂时禁用
    {
        g_combo_state = COMBO_STATE_FIRST_PRESS;
        g_press_start_time = g_combo_timer;
        Key_Matrix_Debug(("COMBO: 1st press start (wakeup)\r\n"));

        extern void User_Start_LED_Blinking(uint8_t count);
        User_Start_LED_Blinking(1);
    }

    // 启动BLE连接
    User_Conn_Start_Connect();
    Key_Matrix_Debug(("BLE connection started\r\n"));

    // 设置初始状态
    g_last_key_mask = 0xA5A5; // 当前是按下状态

    // 启动按键扫描
    g_key_driver.scan_enabled = 1;
    if (!g_key_driver.scan_active)
    {
        g_key_driver.scan_active = 1;
        os_timer_start(&g_key_scan_timer, g_key_driver.config.scan_interval, 1);
        Key_Matrix_Debug(("Key scanning started\r\n"));
    }

    g_wakeup_recovery_needed = 1;

    Key_Matrix_Debug(("Wakeup handler completed\r\n"));
    return 0;
}

/**
 * @brief 检查是否需要重新启动按键扫描（用于初始化后恢复）
 * @return 1-需要启动扫描，0-不需要
 */
int user_key_is_scanning_needed(void)
{
    // 如果有唤醒恢复需求，或者扫描曾经被启用过，就需要重新启动
    return g_wakeup_recovery_needed;
}

/**
 * @brief 检查是否有唤醒按键等待发送（简化版）
 * @return 0-单键遥控器不需要缓存机制
 */
int user_key_has_pending_wakeup(void)
{
    return 0; // 单键遥控器不需要缓存机制
}

/**
 * @brief 获取BLE连接状态
 * @return 0-未连接，1-已连接可发送
 */
int user_key_is_ble_connecting(void) { return User_Ble_Send_EN; }

/**
 * @brief 获取当前PA6按键状态（实时扫描，只读版本）
 * @return 当前按键掩码，0表示无按键按下，1表示PA6按键按下
 */
uint32_t user_key_get_current_hardware_state(void)
{
    if (!g_key_driver.initialized)
    {
        return 0;
    }

    // 直接读取GPIO状态，不触发任何副作用
    int pa6_value = gpio_get_pin_value(GPIO_PORT_A, GPIO_BIT_6);
    if (pa6_value == PA6_KEY_PRESSED_VALUE)
    {
        return 0xA5A5; // 按键按下
    }
    return 0; // 按键未按下
}

// 单键遥控器不需要复杂的BLE超时处理

/**
 * @brief 重置组合动作状态
 */
static void combo_action_reset(void)
{
    g_combo_state = COMBO_STATE_IDLE;
    g_combo_timer = 0;
    g_press_start_time = 0;
    g_release_start_time = 0;
}

/**
 * @brief 执行MAC地址清除操作
 */
static void combo_action_execute_mac_clear(void)
{
    extern int User_Customer_Reset_SlaveMac(void);
    extern void User_Start_LED_Blinking(uint8_t count);

    Key_Matrix_Debug(("=== COMBO EXECUTE: MAC CLEAR (counter=%d) ===\r\n", g_key_press_counter));

    // 重置组合动作状态，但保持检测功能启用
    combo_action_reset();
    // 注意：不再禁用 g_combo_detection_active，保持按键计数功能正常

    // 清除保存的MAC地址
    int result = User_Customer_Reset_SlaveMac();
    if (result == 0)
    {
        Key_Matrix_Debug(("MAC cleared successfully\r\n"));
        Key_Matrix_Debug(("Starting LED blink 5 times for success\r\n"));
        User_Start_LED_Blinking(5);
    }
    else
    {
        Key_Matrix_Debug(("MAC clear failed: %d\r\n", result));
        Key_Matrix_Debug(("Starting LED blink 10 times for failure\r\n"));
        User_Start_LED_Blinking(10);
    }

    Key_Matrix_Debug(("Combo action executed, detection remains active (counter=%d)\r\n", g_key_press_counter));
}

/**
 * @brief 组合动作处理函数
 * @param key_mask 当前按键掩码
 */
static void combo_action_process(uint32_t key_mask)
{
    // 增加计时器
    g_combo_timer++;

    // 检查是否超时
    if (g_combo_timer > COMBO_TIMEOUT_MAX_TIME)
    {
        combo_action_reset();
        return;
    }

    switch (g_combo_state)
    {
    case COMBO_STATE_IDLE:
        if (key_mask != 0)
        {
            // 检测到第一次按键按下
            g_combo_state = COMBO_STATE_FIRST_PRESS;
            g_press_start_time = g_combo_timer;
            Key_Matrix_Debug(("COMBO: 1st press start\r\n"));

            // 立即闪烁1次LED表示第一次按下被识别
            extern void User_Start_LED_Blinking(uint8_t count);
            Key_Matrix_Debug(("Starting LED blink 1 time for 1st press\r\n"));
            User_Start_LED_Blinking(1);
        }
        break;

    case COMBO_STATE_FIRST_PRESS:
        if (key_mask == 0)
        {
            // 第一次按键释放
            uint32_t press_duration = g_combo_timer - g_press_start_time;
            if (press_duration <= COMBO_SHORT_PRESS_MAX_TIME)
            {
                // 短按有效
                g_combo_state = COMBO_STATE_FIRST_RELEASE;
                g_release_start_time = g_combo_timer;
                Key_Matrix_Debug(("COMBO: 1st press OK\r\n"));
            }
            else
            {
                // 按键太长，重置
                Key_Matrix_Debug(("COMBO: 1st press too long, reset\r\n"));
                combo_action_reset();
            }
        }
        break;

    case COMBO_STATE_FIRST_RELEASE:
        if (key_mask != 0)
        {
            // 第二次按键按下
            uint32_t release_duration = g_combo_timer - g_release_start_time;
            if (release_duration <= COMBO_PRESS_INTERVAL_MAX_TIME)
            {
                // 间隔时间合适
                g_combo_state = COMBO_STATE_SECOND_PRESS;
                g_press_start_time = g_combo_timer;
                Key_Matrix_Debug(("COMBO: 2nd press start\r\n"));

                // 立即闪烁2次LED表示第二次按下被识别
                extern void User_Start_LED_Blinking(uint8_t count);
                Key_Matrix_Debug(("Starting LED blink 2 times for 2nd press\r\n"));
                User_Start_LED_Blinking(2);
            }
            else
            {
                // 间隔太长，重置
                Key_Matrix_Debug(("COMBO: interval too long, reset\r\n"));
                combo_action_reset();
            }
        }
        break;

    case COMBO_STATE_SECOND_PRESS:
        if (key_mask == 0)
        {
            // 第二次按键释放
            uint32_t press_duration = g_combo_timer - g_press_start_time;
            if (press_duration <= COMBO_SHORT_PRESS_MAX_TIME)
            {
                // 第二次短按有效
                g_combo_state = COMBO_STATE_SECOND_RELEASE;
                g_release_start_time = g_combo_timer;
                Key_Matrix_Debug(("COMBO: 2nd press OK\r\n"));
            }
            else
            {
                // 按键太长，重置
                Key_Matrix_Debug(("COMBO: 2nd press too long, reset\r\n"));
                combo_action_reset();
            }
        }
        break;

    case COMBO_STATE_SECOND_RELEASE:
        if (key_mask != 0)
        {
            // 第三次按键按下（长按）
            uint32_t release_duration = g_combo_timer - g_release_start_time;
            if (release_duration <= COMBO_PRESS_INTERVAL_MAX_TIME)
            {
                // 间隔时间合适，进入长按状态
                g_combo_state = COMBO_STATE_LONG_PRESS;
                g_press_start_time = g_combo_timer;
                Key_Matrix_Debug(("COMBO: Long press start\r\n"));
            }
            else
            {
                // 间隔太长，重置
                Key_Matrix_Debug(("COMBO: 2nd interval too long, reset\r\n"));
                combo_action_reset();
            }
        }
        break;

    case COMBO_STATE_LONG_PRESS:
        if (key_mask == 0)
        {
            // 长按释放
            uint32_t press_duration = g_combo_timer - g_press_start_time;
            if (press_duration >= COMBO_LONG_PRESS_MIN_TIME)
            {
                // 长按时间足够，执行组合动作
                Key_Matrix_Debug(("COMBO: SUCCESS! MAC clearing...\r\n"));
                combo_action_execute_mac_clear();
            }
            else
            {
                // 长按时间不够，重置
                Key_Matrix_Debug(("COMBO: Long press too short, reset\r\n"));
                combo_action_reset();
            }
        }
        else
        {
            // 仍在长按中，检查是否已经满足最小长按时间
            uint32_t press_duration = g_combo_timer - g_press_start_time;
            if (press_duration >= COMBO_LONG_PRESS_MIN_TIME)
            {
                // 已经满足长按条件，但等待按键释放后再执行组合动作
                Key_Matrix_Debug(("COMBO: Long press time satisfied, waiting for release...\r\n"));
                // 不在这里执行组合动作，等待按键释放时执行
            }
        }
        break;
    }
}

/**
 * @brief 启用/禁用组合动作检测
 * @param enable 1-启用，0-禁用
 */
void user_key_combo_detection_enable(uint8_t enable)
{
    g_combo_detection_active = enable ? 1 : 0;

    if (!g_combo_detection_active)
    {
        // 禁用时重置组合动作状态
        combo_action_reset();
    }

    Key_Matrix_Debug(("Combo detection %s\r\n", enable ? "enabled" : "disabled"));
}

/**
 * @brief 获取当前组合动作检测状态
 * @return 1-启用，0-禁用
 */
uint8_t user_key_combo_detection_status(void)
{
    return g_combo_detection_active;
}

/**
 * @brief 获取当前按键计数值
 * @return 按键计数值
 */
uint8_t user_key_get_press_counter(void)
{
    return g_key_press_counter;
}

/**
 * @brief 清零按键计数值
 */
void user_key_reset_press_counter(void)
{
    g_key_press_counter = 0;
    Key_Matrix_Debug(("Key press counter reset to 0\r\n"));
}

/**
 * @brief 检查是否需要发送按键数据
 * @return 1-需要发送，0-不需要
 */
uint8_t user_key_need_send(void)
{
    return g_need_send_flag;
}

/**
 * @brief 清除发送标志
 */
void user_key_clear_send_flag(void)
{
    g_need_send_flag = 0;
}
