;#<FEEDBACK># ARM Linker, 5060960: Last Updated: Fri Jul 25 15:50:42 2025
;VERSION 0.2
;FILE adv_check_mode.o
__asm___16_adv_check_mode_c_9741a236____REV16 <= USED 0
__asm___16_adv_check_mode_c_9741a236____REVSH <= USED 0
__asm___16_adv_check_mode_c_9741a236____RRX <= USED 0
calib_lld_cancel <= USED 0
calib_lld_init <= USED 0
calib_lld_receive <= USED 0
calib_lld_reset <= USED 0
calib_lld_send <= USED 0
calib_set_freq_config <= USED 0
;FILE app.o
__asm___5_app_c_836484a2____REV16 <= USED 0
__asm___5_app_c_836484a2____REVSH <= USED 0
__asm___5_app_c_836484a2____RRX <= USED 0
ble_aes128_start <= USED 0
ble_gap_get_peer_info <= USED 0
ble_set_diagport <= USED 0
ble_set_ral <= USED 0
ble_set_rlsv_addr_renew_time <= USED 0
ble_set_wl <= USED 0
gap_lecb_add_credit <= USED 0
gap_lecb_conn_req <= USED 0
gap_lecb_disconn_req <= USED 0
gap_lecb_send <= USED 0
gap_lepsm_register <= USED 0
gap_lepsm_unregister <= USED 0
l2cap_recv_data_direct_register <= USED 0
l2cap_send_data_direct <= USED 0
;FILE app_conn.o
appm_conn_send_data <= USED 0
appm_delete_conn_act <= USED 0
;FILE app_mesh_info.o
mesh_info_clear <= USED 0
mesh_info_find_unicast_addr <= USED 0
mesh_info_load <= USED 0
mesh_info_store <= USED 0
mesh_info_store_into_flash <= USED 0
;FILE app_prf.o
;FILE app_proj_event.o
;FILE app_sec.o
ble_sec_clr_fixed_ltk <= USED 0
ble_sec_remove_bond <= USED 0
ble_sec_send_paring_password <= USED 0
ble_sec_send_security_req <= USED 0
ble_sec_set_fixed_ltk <= USED 0
ble_sec_start_bond <= USED 0
ble_sec_start_enc_req <= USED 0
bond_info_add <= USED 0
gap_pairing_rsp <= USED 0
gap_security_check_rand_ediv <= USED 0
;FILE app_task.o
;FILE attc.o
;FILE attm.o
attm_att_set_permission <= USED 0
attm_is_bt16_uuid <= USED 0
attm_is_bt32_uuid <= USED 0
attm_reserve_handle_range <= USED 0
attm_svc_create_db_128 <= USED 0
attm_svc_set_permission <= USED 0
;FILE attm_db.o
attmdb_svc_visibility_set <= USED 0
;FILE atts.o
;FILE core_cm3_isr.o
__asm___14_core_cm3_isr_c_5a2a0181____REV16 <= USED 0
__asm___14_core_cm3_isr_c_5a2a0181____REVSH <= USED 0
__asm___14_core_cm3_isr_c_5a2a0181____RRX <= USED 0
;FILE driver_adc.o
__asm___12_driver_adc_c_15bb8007____REV16 <= USED 0
__asm___12_driver_adc_c_15bb8007____REVSH <= USED 0
__asm___12_driver_adc_c_15bb8007____RRX <= USED 0
adc_get_ref_voltage <= USED 0
;FILE driver_codec.o
audio_speaker_codec_init <= USED 0
codec_adc_init <= USED 0
codec_disable_adc <= USED 0
codec_disable_dac <= USED 0
codec_enable_adc <= USED 0
codec_enable_dac <= USED 0
codec_init <= USED 0
codec_set_vol <= USED 0
speaker_codec_init <= USED 0
;FILE driver_efuse.o
__asm___14_driver_efuse_c_9109bb6a____REV16 <= USED 0
__asm___14_driver_efuse_c_9109bb6a____REVSH <= USED 0
__asm___14_driver_efuse_c_9109bb6a____RRX <= USED 0
efuse_get_chip_unique_id <= USED 0
efuse_write <= USED 0
;FILE driver_exti.o
__asm___13_driver_exti_c_a116d489____REV16 <= USED 0
__asm___13_driver_exti_c_a116d489____REVSH <= USED 0
__asm___13_driver_exti_c_a116d489____RRX <= USED 0
ext_int_clear <= USED 0
ext_int_disable <= USED 0
ext_int_enable <= USED 0
ext_int_get_src <= USED 0
ext_int_set_control <= USED 0
ext_int_set_port_mux <= USED 0
ext_int_set_type <= USED 0
;FILE driver_i2s.o
__asm___12_driver_i2s_c_i2s_reg____REV16 <= USED 0
__asm___12_driver_i2s_c_i2s_reg____REVSH <= USED 0
__asm___12_driver_i2s_c_i2s_reg____RRX <= USED 0
i2s_clear_fifo <= USED 0
i2s_get_data <= USED 0
i2s_get_int_status <= USED 0
i2s_init <= USED 0
i2s_send_data <= USED 0
i2s_start <= USED 0
i2s_stop <= USED 0
;FILE driver_iic.o
__asm___12_driver_iic_c_8d61f799____REV16 <= USED 0
__asm___12_driver_iic_c_8d61f799____REVSH <= USED 0
__asm___12_driver_iic_c_8d61f799____RRX <= USED 0
iic0_isr <= USED 0
iic_init <= USED 0
iic_read_byte <= USED 0
iic_read_bytes <= USED 0
iic_write_byte <= USED 0
iic_write_bytes <= USED 0
iic_write_bytes_imp <= USED 0
;FILE driver_keyscan.o
keyscan_init <= USED 0
keyscan_test <= USED 0
;FILE driver_pdm.o
__asm___12_driver_pdm_c_0ce94caf____REV16 <= USED 0
__asm___12_driver_pdm_c_0ce94caf____REVSH <= USED 0
__asm___12_driver_pdm_c_0ce94caf____RRX <= USED 0
pdm_rxfifo_rptr_clr <= USED 0
pdm_rxfifo_wptr_clr <= USED 0
pdm_start <= USED 0
pdm_stop <= USED 0
pdm_volume_ctrl <= USED 0
;FILE driver_pmu.o
__asm___12_driver_pmu_c_62fa0f09____REV16 <= USED 0
__asm___12_driver_pmu_c_62fa0f09____REVSH <= USED 0
__asm___12_driver_pmu_c_62fa0f09____RRX <= USED 0
pmu_codec_power_disable <= USED 0
pmu_codec_power_enable <= USED 0
pmu_disable_irq <= USED 0
pmu_enable_charge <= USED 0
pmu_get_gpio_port_value <= USED 0
pmu_get_gpio_value <= USED 0
pmu_set_gpio_value <= USED 0
pmu_set_led1_as_pwm <= USED 0
pmu_set_led1_value <= USED 0
pmu_set_led2_as_pwm <= USED 0
pmu_set_led2_value <= USED 0
pmu_set_lp_clk_src <= USED 0
;FILE driver_pmu_pwm.o
__asm___16_driver_pmu_pwm_c_dcc31a5d____REV16 <= USED 0
__asm___16_driver_pmu_pwm_c_dcc31a5d____REVSH <= USED 0
__asm___16_driver_pmu_pwm_c_dcc31a5d____RRX <= USED 0
pmu_pwm_init <= USED 0
pmu_pwm_set_param <= USED 0
pmu_pwm_start <= USED 0
pmu_pwm_stop <= USED 0
;FILE driver_pmu_qdec.o
pmu_qdec_autorest_cnt_flag <= USED 0
pmu_qdec_init <= USED 0
pmu_qdec_set_debounce_cnt <= USED 0
pmu_qdec_set_irq_type <= USED 0
pmu_qdec_set_pin <= USED 0
pmu_qdec_set_threshold <= USED 0
;FILE driver_pwm.o
__asm___12_driver_pwm_c_pwm_ctrl____REV16 <= USED 0
__asm___12_driver_pwm_c_pwm_ctrl____REVSH <= USED 0
__asm___12_driver_pwm_c_pwm_ctrl____RRX <= USED 0
pwm_init <= USED 0
pwm_start <= USED 0
pwm_stop <= USED 0
pwm_update <= USED 0
;FILE driver_rtc.o
__asm___12_driver_rtc_c_rtc_init____REV16 <= USED 0
__asm___12_driver_rtc_c_rtc_init____REVSH <= USED 0
__asm___12_driver_rtc_c_rtc_init____RRX <= USED 0
rtc_alarm <= USED 0
rtc_disalarm <= USED 0
rtc_get_value <= USED 0
rtc_init <= USED 0
;FILE driver_ssp.o
__asm___12_driver_ssp_c_1de10e88____REV16 <= USED 0
__asm___12_driver_ssp_c_1de10e88____REVSH <= USED 0
__asm___12_driver_ssp_c_1de10e88____RRX <= USED 0
ssp_clear_isr_status <= USED 0
ssp_cs_ctrl_function <= USED 0
ssp_disable_interrupt <= USED 0
ssp_enable_interrupt <= USED 0
ssp_get_data_ <= USED 0
ssp_get_isr_status <= USED 0
ssp_init_ <= USED 0
ssp_put_data_to_fifo <= USED 0
ssp_recv_data <= USED 0
ssp_send_120Bytes <= USED 0
ssp_send_byte <= USED 0
ssp_send_bytes <= USED 0
ssp_send_data <= USED 0
ssp_send_then_recv <= USED 0
ssp_wait_send_end <= USED 0
;FILE driver_timer.o
__asm___14_driver_timer_c_89f15fa2____REV16 <= USED 0
__asm___14_driver_timer_c_89f15fa2____REVSH <= USED 0
__asm___14_driver_timer_c_89f15fa2____RRX <= USED 0
timer_get_current_value <= USED 0
timer_get_load_value <= USED 0
timer_init <= USED 0
timer_reload <= USED 0
timer_run <= USED 0
timer_set_load_value <= USED 0
timer_stop <= USED 0
;FILE driver_uart.o
__asm___13_driver_uart_c_a128b2d1____REV16 <= USED 0
__asm___13_driver_uart_c_a128b2d1____REVSH <= USED 0
__asm___13_driver_uart_c_a128b2d1____RRX <= USED 0
uart_init1 <= USED 0
;FILE driver_wdt.o
wdt_test <= USED 0
;FILE entry.o
__asm___7_entry_c_a06f3641____REV16 <= USED 0
__asm___7_entry_c_a06f3641____REVSH <= USED 0
__asm___7_entry_c_a06f3641____RRX <= USED 0
check_and_set_rand_seed <= USED 0
get_SDK_compile_date_time <= USED 0
pmu_recalib_rc <= USED 0
;FILE fal.o
fal_init_check <= USED 0
;FILE fal_flash.o
;FILE fal_flash_wlt8828_port.o
feed_dog <= USED 0
;FILE fal_partition.o
fal_get_partition_table <= USED 0
fal_partition_erase_all <= USED 0
fal_set_partition_table_temp <= USED 0
fal_show_part_table <= USED 0
;FILE fdb.o
_fdb_deinit <= USED 0
;FILE fdb_kvdb.o
fdb_kv_get <= USED 0
fdb_kv_get_obj <= USED 0
fdb_kv_iterate <= USED 0
fdb_kv_iterator_init <= USED 0
fdb_kv_set <= USED 0
fdb_kv_to_blob <= USED 0
fdb_kvdb_deinit <= USED 0
;FILE fdb_utils.o
fdb_blob_read <= USED 0
;FILE flash.o
__asm___7_flash_c_48468d59____REV16 <= USED 0
__asm___7_flash_c_48468d59____REVSH <= USED 0
__asm___7_flash_c_48468d59____RRX <= USED 0
flash_OTP_erase <= USED 0
flash_OTP_protect <= USED 0
flash_OTP_write <= USED 0
flash_protect_disable <= USED 0
flash_protect_enable <= USED 0
flash_protect_enable1 <= USED 0
flash_read_id <= USED 0
;FILE gap_api.o
gap_bond_manager_add_info <= USED 0
gap_conn_phy_update <= USED 0
gap_get_conn_phy <= USED 0
gap_get_connect_num <= USED 0
gap_get_encryption_status <= USED 0
gap_get_latest_bond_idx <= USED 0
gap_get_latest_conn_parameter <= USED 0
gap_get_link_features <= USED 0
gap_get_link_rssi <= USED 0
gap_get_link_version <= USED 0
gap_sec_clr_fixed_ltk <= USED 0
gap_sec_set_fixed_ltk <= USED 0
gap_security_enc_req <= USED 0
gap_security_get_bond_req <= USED 0
gap_security_get_bond_status <= USED 0
gap_security_pairing_req <= USED 0
gap_security_req <= USED 0
gap_security_send_pairing_password <= USED 0
gap_set_advertising_data1 <= USED 0
gap_set_advertising_param1 <= USED 0
gap_set_advertising_rsp_data1 <= USED 0
gap_set_channel_map <= USED 0
gap_set_per_adv_data <= USED 0
gap_set_per_adv_data1 <= USED 0
gap_set_ral <= USED 0
gap_set_wl <= USED 0
gap_start_advertising1 <= USED 0
gap_start_conn_long_range <= USED 0
gap_start_conn_whitelist <= USED 0
gap_start_per_sync <= USED 0
gap_stop_advertising1 <= USED 0
gap_stop_per_sync <= USED 0
;FILE gapc.o
;FILE gapc_hci.o
;FILE gapc_sig.o
;FILE gapc_task.o
;FILE gapm.o
gapm_get_id_from_task <= USED 0
gapm_get_task_from_id <= USED 0
;FILE gapm_actv.o
;FILE gapm_addr.o
;FILE gapm_adv.o
;FILE gapm_cfg.o
;FILE gapm_init.o
;FILE gapm_list.o
;FILE gapm_per_sync.o
;FILE gapm_scan.o
;FILE gapm_smp.o
;FILE gatt_api.o
gatt_change_client_uuid <= USED 0
gatt_change_svc <= USED 0
gatt_client_enable_ntf_ind <= USED 0
gatt_client_read <= USED 0
gatt_client_read_by_uuid <= USED 0
gatt_client_read_with_handle <= USED 0
gatt_client_write_req <= USED 0
gatt_delete_svc <= USED 0
gatt_delete_svc_list <= USED 0
gatt_discovery_all_peer_svc <= USED 0
gatt_discovery_peer_svc <= USED 0
gatt_get_client_read_handle <= USED 0
gatt_get_notification_handle <= USED 0
gatt_get_svc_hdl <= USED 0
gatt_get_task_no_from_prf_id <= USED 0
gatt_indication <= USED 0
gatt_indication_with_handle <= USED 0
gatt_msg_default <= USED 0
gatt_svc_changed_req <= USED 0
;FILE gattc.o
gattc_get_op_seq_num <= USED 0
;FILE gattc_task.o
gattc_trans_timeout_ctrl <= USED 0
;FILE gattm.o
;FILE jump_table.o
__asm___12_jump_table_c_5f817404____REV16 <= USED 0
__asm___12_jump_table_c_5f817404____REVSH <= USED 0
__asm___12_jump_table_c_5f817404____RRX <= USED 0
;FILE l2cc.o
;FILE l2cc_lecb.o
;FILE l2cc_pdu.o
l2cc_dbg_pdu_unpack <= USED 0
;FILE l2cc_sig.o
;FILE l2cc_task.o
;FILE l2cm.o
;FILE m_al_activity.o
gapm_activity_created_ind_handler <= USED 0
gapm_activity_stopped_ind_handler <= USED 0
m_al_activity_cmd_send <= USED 0
m_al_activity_cmp_evt_handler <= USED 0
m_al_activity_init <= USED 0
;FILE m_al_adv.o
m_al_adv_cmp_evt_handler <= USED 0
m_al_adv_created_ind_handler <= USED 0
m_al_adv_init <= USED 0
m_al_adv_stopped_ind_handler <= USED 0
;FILE m_al_con.o
gattc_cmp_evt_handler <= USED 0
gattc_read_req_ind_handler <= USED 0
gattc_write_req_ind_handler <= USED 0
m_al_con_cleanup <= USED 0
m_al_con_cmp_evt_handler <= USED 0
m_al_con_create <= USED 0
m_al_con_created_ind_handler <= USED 0
m_al_con_init <= USED 0
m_al_con_stopped_ind_handler <= USED 0
mesh_con_timer_ind_handler <= USED 0
;FILE m_al_djob.o
m_al_djob_init <= USED 0
m_al_djob_reg <= USED 0
;FILE m_al_scan.o
gapm_ext_adv_report_ind_handler <= USED 0
m_al_scan_cmp_evt_handler <= USED 0
m_al_scan_created_ind_handler <= USED 0
m_al_scan_init <= USED 0
m_al_scan_start <= USED 0
m_al_scan_stop <= USED 0
m_al_scan_stopped_ind_handler <= USED 0
;FILE m_al_sec.o
gapm_gen_dh_key_ind_handler <= USED 0
gapm_pub_key_ind_handler <= USED 0
gapm_use_enc_block_ind_handler <= USED 0
m_al_sec_aes <= USED 0
m_al_sec_aes_ccm <= USED 0
m_al_sec_aes_cmac <= USED 0
m_al_sec_aes_k1 <= USED 0
m_al_sec_aes_k2 <= USED 0
m_al_sec_aes_k3 <= USED 0
m_al_sec_aes_k4 <= USED 0
m_al_sec_aes_req <= USED 0
m_al_sec_aes_s1 <= USED 0
m_al_sec_cmp_evt_handler <= USED 0
m_al_sec_ecdh_secret <= USED 0
m_al_sec_init <= USED 0
m_al_sec_pub_key_read <= USED 0
m_al_sec_rand <= USED 0
;FILE m_al_task.o
m_al_free <= USED 0
m_al_int_message_handler <= USED 0
m_al_local_id_get <= USED 0
m_al_malloc <= USED 0
m_al_msg_alloc <= USED 0
m_al_msg_free <= USED 0
m_al_msg_send <= USED 0
m_al_msg_src_id_get <= USED 0
m_al_prf_itf_get <= USED 0
m_al_set_local_addr <= USED 0
;FILE m_al_timer.o
m_al_timer_clear <= USED 0
m_al_timer_get_clock <= USED 0
m_al_timer_get_delay <= USED 0
m_al_timer_get_time_ms <= USED 0
m_al_timer_init <= USED 0
m_al_timer_set <= USED 0
m_al_timer_set_clock <= USED 0
mesh_timer_ind_handler <= USED 0
;FILE m_api.o
m_api_buf_alloc <= USED 0
m_api_buf_alloc_block <= USED 0
m_api_buf_data_get <= USED 0
m_api_buf_data_len_get <= USED 0
m_api_buf_free_block <= USED 0
m_api_buf_release <= USED 0
m_api_compo_data_cfm <= USED 0
m_api_disable <= USED 0
m_api_enable <= USED 0
m_api_get_env_size <= USED 0
m_api_get_run_time <= USED 0
m_api_health_cfm <= USED 0
m_api_health_status_send <= USED 0
m_api_init <= USED 0
m_api_iv_upd_test_mode <= USED 0
m_api_load_stored_info <= USED 0
m_api_lpn_select_friend <= USED 0
m_api_lpn_start <= USED 0
m_api_lpn_stop <= USED 0
m_api_message_handler <= USED 0
m_api_model_opcode_status <= USED 0
m_api_model_publish <= USED 0
m_api_model_rsp_send <= USED 0
m_api_prov_oob_auth_rsp <= USED 0
m_api_prov_param_rsp <= USED 0
m_api_prov_pub_key_read <= USED 0
m_api_prov_stop_link_timeout <= USED 0
m_api_proxy_ctrl <= USED 0
m_api_proxy_end_ind <= USED 0
m_api_register_model <= USED 0
m_api_send_attention_update_ind <= USED 0
m_api_send_compo_data_req_ind <= USED 0
m_api_send_fault_clear_ind <= USED 0
m_api_send_fault_get_req_ind <= USED 0
m_api_send_fault_period_ind <= USED 0
m_api_send_fault_test_req_ind <= USED 0
m_api_send_lpn_offer_ind <= USED 0
m_api_send_lpn_status_ind <= USED 0
m_api_send_node_reset_ind <= USED 0
m_api_send_update_ind <= USED 0
m_api_set <= USED 0
m_api_set_run_time <= USED 0
;FILE m_api_msg.o
m_api_msg_handler <= USED 0
m_api_msg_init <= USED 0
;FILE m_bcn.o
m_bcn_connect_api <= USED 0
m_bcn_get_env_size <= USED 0
m_bcn_init <= USED 0
m_bcn_proxy_con_start <= USED 0
m_bcn_restart_tx_unprov_bcn <= USED 0
m_bcn_start_tx_unprov_bcn <= USED 0
m_bcn_state_update <= USED 0
m_bcn_stop_tx_security_bcn <= USED 0
m_bcn_stop_tx_unprov_bcn <= USED 0
m_bcn_subnet_update_ind <= USED 0
;FILE m_bearer.o
m_bearer_closed <= USED 0
m_bearer_connect_client_cb <= USED 0
m_bearer_get_env_size <= USED 0
m_bearer_init <= USED 0
m_bearer_opened <= USED 0
m_bearer_rx <= USED 0
m_bearer_sent <= USED 0
m_bearer_start <= USED 0
m_bearer_started <= USED 0
m_bearer_stop <= USED 0
m_bearer_stopped <= USED 0
;FILE m_bearer_adv.o
m_bearer_adv_connect_api_al <= USED 0
m_bearer_adv_send <= USED 0
m_bearer_adv_start <= USED 0
m_bearer_adv_stop <= USED 0
m_bearer_adv_tx_prepare <= USED 0
m_bearer_adv_update <= USED 0
;FILE m_bearer_gatt.o
m_bearer_gatt_connect_api_al <= USED 0
m_bearer_gatt_send <= USED 0
m_bearer_gatt_start <= USED 0
m_bearer_gatt_stop <= USED 0
m_bearer_gatt_tx_prepare <= USED 0
m_bearer_gatt_update <= USED 0
;FILE m_fnd.o
m_fnd_get_env_size <= USED 0
m_fnd_init <= USED 0
;FILE m_fnd_confs.o
m_fnd_confs_get_env_size <= USED 0
m_fnd_confs_init <= USED 0
;FILE m_fnd_hlths.o
m_fnd_hlths_cfm <= USED 0
m_fnd_hlths_get_env_size <= USED 0
m_fnd_hlths_init <= USED 0
m_fnd_hlths_status_ind <= USED 0
;FILE m_lay.o
m_lay_get_env_size <= USED 0
m_lay_init <= USED 0
;FILE m_lay_access.o
m_lay_access_connect_api_utrans <= USED 0
m_lay_access_get_env_size <= USED 0
m_lay_access_init <= USED 0
m_lay_access_opcode_status <= USED 0
m_lay_access_publish <= USED 0
m_lay_access_rsp_send <= USED 0
;FILE m_lay_friend.o
m_lay_friend_cb_state_updated <= USED 0
m_lay_friend_connect_api_net <= USED 0
m_lay_friend_connect_api_utrans <= USED 0
m_lay_friend_get_env_size <= USED 0
m_lay_friend_init <= USED 0
;FILE m_lay_hb.o
m_lay_hb_cb_state_updated <= USED 0
m_lay_hb_connect_api_utrans <= USED 0
m_lay_hb_get_env_size <= USED 0
m_lay_hb_init <= USED 0
;FILE m_lay_lpn.o
m_lay_lpn_connect_api_utrans <= USED 0
m_lay_lpn_get_env_size <= USED 0
m_lay_lpn_init <= USED 0
m_lay_lpn_select_friend <= USED 0
m_lay_lpn_start <= USED 0
m_lay_lpn_stop <= USED 0
;FILE m_lay_ltrans.o
m_lay_ltrans_connect_api_net <= USED 0
m_lay_ltrans_connect_cb <= USED 0
m_lay_ltrans_connect_cb_seg <= USED 0
m_lay_ltrans_get_env_size <= USED 0
m_lay_ltrans_init <= USED 0
;FILE m_lay_net.o
m_lay_net_connect_api_bearer <= USED 0
m_lay_net_connect_cb <= USED 0
m_lay_net_connect_cb_lpn_rx <= USED 0
m_lay_net_get_env_size <= USED 0
m_lay_net_get_sending_buf_in_queue <= USED 0
m_lay_net_init <= USED 0
;FILE m_lay_proxy.o
m_lay_proxy_bearer_adv_ctrl <= USED 0
m_lay_proxy_bearer_connect_api <= USED 0
m_lay_proxy_connect_api_net <= USED 0
m_lay_proxy_get_env_size <= USED 0
m_lay_proxy_init <= USED 0
m_lay_proxy_state_set <= USED 0
;FILE m_lay_utrans.o
m_lay_utrans_connect_api_ltrans <= USED 0
m_lay_utrans_connect_cb <= USED 0
m_lay_utrans_get_env_size <= USED 0
m_lay_utrans_init <= USED 0
;FILE m_prov.o
m_prov_get_env_size <= USED 0
m_prov_init <= USED 0
m_prov_link_closed <= USED 0
m_prov_link_open <= USED 0
m_prov_oob_auth_rsp <= USED 0
m_prov_param_rsp <= USED 0
m_prov_pdu_rx <= USED 0
m_prov_pdu_sent <= USED 0
m_prov_pub_key_get <= USED 0
m_prov_start <= USED 0
m_prov_stop_link_timeout <= USED 0
;FILE m_prov_adv_trans.o
m_prov_adv_trans_clean_up <= USED 0
m_prov_adv_trans_link_close_send <= USED 0
m_prov_adv_trans_rx <= USED 0
m_prov_adv_trans_send <= USED 0
m_prov_adv_trans_sent <= USED 0
;FILE m_prov_bearer.o
m_prov_bearer_close <= USED 0
m_prov_bearer_connect_api <= USED 0
m_prov_bearer_gatt_send <= USED 0
m_prov_bearer_gatt_start <= USED 0
m_prov_bearer_gatt_stop <= USED 0
m_prov_bearer_init <= USED 0
m_prov_bearer_scan_start <= USED 0
m_prov_bearer_scan_stop <= USED 0
m_prov_bearer_send <= USED 0
;FILE m_tb.o
m_tb_get_env_size <= USED 0
m_tb_init <= USED 0
;FILE m_tb_buf.o
m_tb_buf_acquire <= USED 0
m_tb_buf_alloc <= USED 0
m_tb_buf_block_alloc <= USED 0
m_tb_buf_block_free <= USED 0
m_tb_buf_copy <= USED 0
m_tb_buf_copy_data_from_mem <= USED 0
m_tb_buf_copy_data_to_mem <= USED 0
m_tb_buf_get_env_size <= USED 0
m_tb_buf_head_release <= USED 0
m_tb_buf_head_reserve <= USED 0
m_tb_buf_init <= USED 0
m_tb_buf_release <= USED 0
m_tb_buf_reuse <= USED 0
m_tb_buf_tail_release <= USED 0
m_tb_buf_tail_reserve <= USED 0
;FILE m_tb_friend.o
m_tb_friend_get_env_size <= USED 0
m_tb_friend_get_friend_cnt <= USED 0
m_tb_friend_get_nb_lpn_estab <= USED 0
m_tb_friend_get_nb_lpn_known <= USED 0
m_tb_friend_get_polltimeout_ms <= USED 0
m_tb_friend_init <= USED 0
m_tb_friend_is_lpn <= USED 0
m_tb_friend_is_subs_dst <= USED 0
m_tb_friend_lpn_add <= USED 0
m_tb_friend_lpn_estab <= USED 0
m_tb_friend_lpn_rem <= USED 0
m_tb_friend_subs_list_update <= USED 0
;FILE m_tb_key.o
m_tb_key_app_add <= USED 0
m_tb_key_app_delete <= USED 0
m_tb_key_app_find <= USED 0
m_tb_key_app_get <= USED 0
m_tb_key_app_get_ids <= USED 0
m_tb_key_app_get_lids <= USED 0
m_tb_key_app_lid_from_net_aid <= USED 0
m_tb_key_app_update <= USED 0
m_tb_key_dev_add <= USED 0
m_tb_key_dev_get <= USED 0
m_tb_key_friend_end_ind <= USED 0
m_tb_key_friend_get_key <= USED 0
m_tb_key_friend_get_nid <= USED 0
m_tb_key_friend_net_lid_from_nid <= USED 0
m_tb_key_friend_new_ind <= USED 0
m_tb_key_get_env_size <= USED 0
m_tb_key_get_iv_rx <= USED 0
m_tb_key_get_iv_seq <= USED 0
m_tb_key_get_iv_seq_no_inc <= USED 0
m_tb_key_get_model_appkey_ids <= USED 0
m_tb_key_get_nb_app_keys <= USED 0
m_tb_key_get_nb_net_keys <= USED 0
m_tb_key_get_net_from_net_id <= USED 0
m_tb_key_get_net_info <= USED 0
m_tb_key_get_netkey_id <= USED 0
m_tb_key_get_node_identity <= USED 0
m_tb_key_get_phase <= USED 0
m_tb_key_init <= USED 0
m_tb_key_iv_upd_test_mode_ind <= USED 0
m_tb_key_model_bind <= USED 0
m_tb_key_model_bind_check <= USED 0
m_tb_key_model_unbind <= USED 0
m_tb_key_net_add <= USED 0
m_tb_key_net_delete <= USED 0
m_tb_key_net_find <= USED 0
m_tb_key_net_get <= USED 0
m_tb_key_net_get_ids <= USED 0
m_tb_key_net_lid_from_nid <= USED 0
m_tb_key_net_next <= USED 0
m_tb_key_net_revoke_old <= USED 0
m_tb_key_net_update <= USED 0
m_tb_key_net_use_new <= USED 0
m_tb_key_phase_transition_ind <= USED 0
m_tb_key_secure_bcn_rx_ind <= USED 0
m_tb_key_set_iv_seq <= USED 0
m_tb_key_set_node_identity <= USED 0
;FILE m_tb_mio.o
m_tb_mio_add_subscription <= USED 0
m_tb_mio_add_subscription_virt <= USED 0
m_tb_mio_addr_list_get <= USED 0
m_tb_mio_addr_list_get_size <= USED 0
m_tb_mio_addr_list_start <= USED 0
m_tb_mio_addr_list_stop <= USED 0
m_tb_mio_app_key_rem_ind <= USED 0
m_tb_mio_bind <= USED 0
m_tb_mio_cb_get <= USED 0
m_tb_mio_delete_all_subscription <= USED 0
m_tb_mio_delete_subscription <= USED 0
m_tb_mio_delete_subscription_virt <= USED 0
m_tb_mio_get_element_addr <= USED 0
m_tb_mio_get_env_size <= USED 0
m_tb_mio_get_label_uuid <= USED 0
m_tb_mio_get_local_id <= USED 0
m_tb_mio_get_model_id <= USED 0
m_tb_mio_get_model_ids <= USED 0
m_tb_mio_get_nb_bound_app <= USED 0
m_tb_mio_get_nb_elements <= USED 0
m_tb_mio_get_nb_model <= USED 0
m_tb_mio_get_nb_models <= USED 0
m_tb_mio_get_period <= USED 0
m_tb_mio_get_prim_addr <= USED 0
m_tb_mio_get_publi_param <= USED 0
m_tb_mio_get_subscription_list <= USED 0
m_tb_mio_get_subscription_list_size <= USED 0
m_tb_mio_get_subscription_list_size_vaddr <= USED 0
m_tb_mio_get_vaddr <= USED 0
m_tb_mio_init <= USED 0
m_tb_mio_is_dest_addr <= USED 0
m_tb_mio_is_local_addr <= USED 0
m_tb_mio_register_model <= USED 0
m_tb_mio_set_prim_addr <= USED 0
m_tb_mio_set_publi_param <= USED 0
m_tb_mio_unbind <= USED 0
;FILE m_tb_sec.o
m_tb_sec_aes_start <= USED 0
m_tb_sec_ccm_dec_start <= USED 0
m_tb_sec_ccm_enc_start <= USED 0
m_tb_sec_cmac_start <= USED 0
m_tb_sec_get_env_size <= USED 0
m_tb_sec_init <= USED 0
m_tb_sec_k1_start <= USED 0
m_tb_sec_k2_start <= USED 0
m_tb_sec_k3_start <= USED 0
m_tb_sec_k4_start <= USED 0
m_tb_sec_rand_start <= USED 0
m_tb_sec_s1_start <= USED 0
;FILE m_tb_state.o
m_tb_state_get_attention_state <= USED 0
m_tb_state_get_beacon_state <= USED 0
m_tb_state_get_compo_info <= USED 0
m_tb_state_get_default_ttl <= USED 0
m_tb_state_get_env_size <= USED 0
m_tb_state_get_friend_state <= USED 0
m_tb_state_get_gatt_proxy_state <= USED 0
m_tb_state_get_hb_feat_upd <= USED 0
m_tb_state_get_hb_pub_params <= USED 0
m_tb_state_get_hb_subs_params <= USED 0
m_tb_state_get_lpn_state <= USED 0
m_tb_state_get_nb_cdata_page <= USED 0
m_tb_state_get_net_tx_params <= USED 0
m_tb_state_get_net_tx_state <= USED 0
m_tb_state_get_prov_state <= USED 0
m_tb_state_get_relay_state <= USED 0
m_tb_state_hb_check_features <= USED 0
m_tb_state_hb_rx_ind <= USED 0
m_tb_state_hb_tx_ind <= USED 0
m_tb_state_init <= USED 0
m_tb_state_is_enabled <= USED 0
m_tb_state_is_feature_sup <= USED 0
m_tb_state_is_iv_update_auth <= USED 0
m_tb_state_iv_update_ind <= USED 0
m_tb_state_net_key_rem_ind <= USED 0
m_tb_state_set_attention_state <= USED 0
m_tb_state_set_beacon_state <= USED 0
m_tb_state_set_default_ttl <= USED 0
m_tb_state_set_enabled <= USED 0
m_tb_state_set_friend_cb <= USED 0
m_tb_state_set_friend_state <= USED 0
m_tb_state_set_gatt_proxy_state <= USED 0
m_tb_state_set_hb_cb <= USED 0
m_tb_state_set_hb_pub_params <= USED 0
m_tb_state_set_hb_subs_params <= USED 0
m_tb_state_set_lpn_state <= USED 0
m_tb_state_set_net_tx_state <= USED 0
m_tb_state_set_prov_state <= USED 0
m_tb_state_set_relay_state <= USED 0
;FILE m_tb_store_wvt.o
m_tb_store_get_compo_data <= USED 0
m_tb_store_get_env_size <= USED 0
m_tb_store_init <= USED 0
m_tb_store_load <= USED 0
m_tb_store_rx_compo_data <= USED 0
m_tb_store_update_ind <= USED 0
;FILE m_tb_timer.o
m_tb_timer_clear <= USED 0
m_tb_timer_expired <= USED 0
m_tb_timer_get_cur_time <= USED 0
m_tb_timer_get_env_size <= USED 0
m_tb_timer_init <= USED 0
m_tb_timer_set <= USED 0
;FILE mesh_api.o
app_mesh_enable_prf <= USED 0
mesh_add_model <= USED 0
mesh_adv_report_ind_user_handler <= USED 0
mesh_api_compo_data_req_ind_handler <= USED 0
mesh_api_fault_get_req_ind_handler <= USED 0
mesh_api_model_opcode_check <= USED 0
mesh_api_proxy_end_ind_handler <= USED 0
mesh_get_node_unicast_addr <= USED 0
mesh_get_remote_param <= USED 0
mesh_init <= USED 0
mesh_iv_update <= USED 0
mesh_load_stored_info <= USED 0
mesh_model_add_publish_addr <= USED 0
mesh_model_bind_appkey <= USED 0
mesh_model_del_group_addr <= USED 0
mesh_model_opcode_check <= USED 0
mesh_model_sub_group_addr <= USED 0
mesh_proxy_ctrl <= USED 0
mesh_publish_msg <= USED 0
mesh_send_compo_data_rsp <= USED 0
mesh_send_prov_auth_data_rsp <= USED 0
mesh_send_prov_param_rsp <= USED 0
mesh_send_rsp <= USED 0
mesh_set_adv_parameter <= USED 0
mesh_set_cb_func <= USED 0
mesh_set_scan_parameter <= USED 0
mesh_set_scan_rsp_data <= USED 0
mesh_start <= USED 0
mesh_stop <= USED 0
mesh_stop_prov_link_timeout_timer <= USED 0
;FILE os_msg_q.o
os_msg_free <= USED 0
os_msg_malloc <= USED 0
os_msg_send <= USED 0
;FILE os_task.o
ke_task_delete <= USED 0
ke_task_saved_update <= USED 0
os_get_free_task_id_num <= USED 0
os_get_free_task_type <= USED 0
os_task_create <= USED 0
os_task_create_x <= USED 0
os_task_delete <= USED 0
os_task_process_saved_msg <= USED 0
;FILE os_timer.o
;FILE ota.o
__asm___5_ota_c_56ada2da____REV16 <= USED 0
__asm___5_ota_c_56ada2da____REVSH <= USED 0
__asm___5_ota_c_56ada2da____RRX <= USED 0
;FILE ota_service.o
__asm___13_ota_service_c_0613aef6____REV16 <= USED 0
__asm___13_ota_service_c_0613aef6____REVSH <= USED 0
__asm___13_ota_service_c_0613aef6____RRX <= USED 0
ota_gatt_op_cmp_handler <= USED 0
;FILE patch.o
__asm___7_patch_c_487891fc____REV16 <= USED 0
__asm___7_patch_c_487891fc____REVSH <= USED 0
__asm___7_patch_c_487891fc____RRX <= USED 0
;FILE patch_no_md_bit.o
dbg_log <= USED 0
gatt_get_tx_buf_nb <= USED 0
patch_get_con_evt_cnt <= USED 0
patch_set_event_cnt_target <= USED 0
;FILE patch_rf.o
__asm___10_patch_rf_c_e73c1e89____REV16 <= USED 0
__asm___10_patch_rf_c_e73c1e89____REVSH <= USED 0
__asm___10_patch_rf_c_e73c1e89____RRX <= USED 0
lc_update_check <= USED 0
lld_con_tx_remove_duplicate <= USED 0
system_set_conn_sleep_max_during <= USED 0
;FILE patch_rtos.o
__asm___12_patch_rtos_c_ea38be2b____REV16 <= USED 0
__asm___12_patch_rtos_c_ea38be2b____REVSH <= USED 0
__asm___12_patch_rtos_c_ea38be2b____RRX <= USED 0
freertos_baseband_restore_done_patch <= USED 0
;FILE prf.o
prf_dst_task_get <= USED 0
prf_env_get <= USED 0
prf_get_id_from_task <= USED 0
prf_src_task_get <= USED 0
;FILE prf_utils.o
prf_check_svc_char_desc_validity <= USED 0
prf_check_svc_char_validity <= USED 0
prf_client_att_info_rsp <= USED 0
prf_disc_svc_send <= USED 0
prf_extract_svc_info <= USED 0
prf_gatt_write <= USED 0
prf_gatt_write_ntf_ind <= USED 0
prf_pack_date_time <= USED 0
prf_read_char_send <= USED 0
prf_register_atthdl2gatt <= USED 0
prf_unpack_date_time <= USED 0
prf_unregister_atthdl2gatt <= USED 0
;FILE proj_main.o
__asm___11_proj_main_c_571a19f4____REV16 <= USED 0
__asm___11_proj_main_c_571a19f4____REVSH <= USED 0
__asm___11_proj_main_c_571a19f4____RRX <= USED 0
;FILE rwble_hl.o
;FILE smpc.o
;FILE smpc_api.o
;FILE smpc_crypto.o
;FILE smpc_util.o
;FILE sys_utils.o
Smoothing_Filter_destroy <= USED 0
Smoothing_Filter_handle <= USED 0
Smoothing_Filter_init <= USED 0
Smoothing_Filter_reset <= USED 0
__asm___11_sys_utils_c_415ab936____REV16 <= USED 0
__asm___11_sys_utils_c_415ab936____REVSH <= USED 0
__asm___11_sys_utils_c_415ab936____RRX <= USED 0
_system_power_off <= USED 0
aes_decrypt <= USED 0
ascii_strn2val <= USED 0
char_to_val <= USED 0
hex4bit_to_caps_char <= USED 0
hex4bit_to_char <= USED 0
hex_arr_to_str <= USED 0
kalmanFilter <= USED 0
retry_handshake <= USED 0
str_to_hex_arr <= USED 0
str_to_val <= USED 0
system_get_spare_exmem <= USED 0
system_prevent_sleep_clear <= USED 0
system_prevent_sleep_set <= USED 0
system_set_rf_power <= USED 0
val_to_str <= USED 0
;FILE user_bond_manage.o
ble_bond_addr_check_result <= USED 0
ble_bond_get_last_bond_idx <= USED 0
gatt_load_peer_svc_from_flash <= USED 0
;FILE user_deviceinfo_service.o
dis_gatt_op_cmp_handler <= USED 0
;FILE user_mem.o
ke_free_user <= USED 0
ke_get_mem_max_usage <= USED 0
ke_malloc_user <= USED 0
show_ke_malloc <= USED 0
show_mem_list <= USED 0
show_msg_list <= USED 0
;FILE user_produce_test.o
__asm___19_user_produce_test_c_3acad816____REV16 <= USED 0
__asm___19_user_produce_test_c_3acad816____REVSH <= USED 0
__asm___19_user_produce_test_c_3acad816____RRX <= USED 0
;FILE user_produce_test_ble.o
;FILE user_spple_service.o
user_spple_gatt_add_service <= USED 0
;FILE userapp.o
User_Flash_Protect_Init <= USED 0
User_Get_Now_SendIng_Len <= USED 0
User_Get_Throughput_Pin <= USED 0
User_Set_Flash_Lock <= USED 0
User_Set_Flash_UnLock <= USED 0
User_Set_MTU_OnConnected <= USED 0
User_Show_Mem <= USED 0
User_Start_Conn_For_ADV_Process <= USED 0
User_Start_Conn_For_ADV_Scan <= USED 0
__asm___9_UserApp_c_27b6ebc5____REV16 <= USED 0
__asm___9_UserApp_c_27b6ebc5____REVSH <= USED 0
__asm___9_UserApp_c_27b6ebc5____RRX <= USED 0
generateRandomNum <= USED 0
get_multi_system_time_flag <= USED 0
get_scan_filter_name <= USED 0
get_scan_filter_name_flag <= USED 0
set_multi_system_time_flag <= USED 0
;FILE useratcmd.o
Ble_Cmd_Init <= USED 0
Ble_Cmd_Input_Data_Read <= USED 0
Ble_Cmd_Input_Data_Write <= USED 0
Ble_Cmd_Output_Data_Read <= USED 0
Set_Cmd_Output_Use_BLE <= USED 0
Set_Cmd_Output_Use_Uart <= USED 0
__asm___11_UserATCmd_c_d2b14370____REV16 <= USED 0
__asm___11_UserATCmd_c_d2b14370____REVSH <= USED 0
__asm___11_UserATCmd_c_d2b14370____RRX <= USED 0
;FILE userble.o
__asm___9_UserBLE_c_40cc9220____REV16 <= USED 0
__asm___9_UserBLE_c_40cc9220____REVSH <= USED 0
__asm___9_UserBLE_c_40cc9220____RRX <= USED 0
;FILE usercustomer.o
User_Customer_Get_RemoteID <= USED 0
User_Customer_Get_StudyRemoteID0 <= USED 0
User_Customer_Get_StudyRemoteID1 <= USED 0
User_Customer_Reset_StudyRemoteID0 <= USED 0
User_Customer_Reset_StudyRemoteID1 <= USED 0
User_Customer_Set_RemoteID <= USED 0
User_Customer_Set_SlaveMac <= USED 0
User_Customer_Set_StudyRemoteID0 <= USED 0
User_Customer_Set_StudyRemoteID1 <= USED 0
User_Customer_Uart_Timer_Close <= USED 0
User_Customer_Uart_Timer_Init <= USED 0
__asm___14_UserCustomer_c_2fedafbe____REV16 <= USED 0
__asm___14_UserCustomer_c_2fedafbe____REVSH <= USED 0
__asm___14_UserCustomer_c_2fedafbe____RRX <= USED 0
;FILE userkeyadv.o
User_Is_LED_Blinking <= USED 0
User_Start_LED_Blinking <= USED 0
User_Stop_LED_Blinking <= USED 0
__asm___12_UserKeyAdv_c_faf598ca____REV16 <= USED 0
__asm___12_UserKeyAdv_c_faf598ca____REVSH <= USED 0
__asm___12_UserKeyAdv_c_faf598ca____RRX <= USED 0
;FILE userkeymatrix.o
__asm___15_UserKeyMatrix_c_a0fedbbd____REV16 <= USED 0
__asm___15_UserKeyMatrix_c_a0fedbbd____REVSH <= USED 0
__asm___15_UserKeyMatrix_c_a0fedbbd____RRX <= USED 0
user_key_combo_detection_enable <= USED 0
user_key_combo_detection_status <= USED 0
user_key_has_pending_wakeup <= USED 0
user_key_is_ble_connecting <= USED 0
user_key_reset_press_counter <= USED 0
;FILE userpla.o
User_PLA_Enter_Sleep_From_BLE_Conn <= USED 0
User_PLA_GPIO_Init <= USED 0
User_PLA_Sleep_Init <= USED 0
__asm___9_UserPLA_c_e28f5c5f____REV16 <= USED 0
__asm___9_UserPLA_c_e28f5c5f____REVSH <= USED 0
__asm___9_UserPLA_c_e28f5c5f____RRX <= USED 0
;FILE userprintf.o
;FILE useruart.o
HAL_UART_Int_GetRxFree <= USED 0
__asm___10_UserUart_c_190c0717____REV16 <= USED 0
__asm___10_UserUart_c_190c0717____REVSH <= USED 0
__asm___10_UserUart_c_190c0717____RRX <= USED 0
;FILE useruartupdata.o
__asm___16_UserUartUpdata_c_1de028d0____REV16 <= USED 0
__asm___16_UserUartUpdata_c_1de028d0____REVSH <= USED 0
__asm___16_UserUartUpdata_c_1de028d0____RRX <= USED 0
;FILE wlt_kv.o
wlt_kv_demo <= USED 0
wlt_kv_getfloat <= USED 0
wlt_kv_getint <= USED 0
wlt_kv_getstring <= USED 0
wlt_kv_reset <= USED 0
wlt_kv_setfloat <= USED 0
wlt_kv_setint <= USED 0
wlt_kv_setstring <= USED 0
;FILE wlt_util.o
bd_addr_to_str <= USED 0
bd_address_to_str <= USED 0
char_for_nibble <= USED 0
wlt_atoi <= USED 0
wlt_big_endian_read_16 <= USED 0
wlt_big_endian_read_24 <= USED 0
wlt_big_endian_read_32 <= USED 0
wlt_big_endian_store_16 <= USED 0
wlt_big_endian_store_24 <= USED 0
wlt_big_endian_store_32 <= USED 0
wlt_char_for_nibble <= USED 0
wlt_count_set_bits_uint32 <= USED 0
wlt_crc8_calc <= USED 0
wlt_crc8_check <= USED 0
wlt_little_endian_read_16 <= USED 0
wlt_little_endian_read_24 <= USED 0
wlt_little_endian_read_32 <= USED 0
wlt_little_endian_store_16 <= USED 0
wlt_little_endian_store_24 <= USED 0
wlt_little_endian_store_32 <= USED 0
wlt_max <= USED 0
wlt_min <= USED 0
wlt_nibble_for_char <= USED 0
wlt_reverse_128 <= USED 0
wlt_reverse_24 <= USED 0
wlt_reverse_256 <= USED 0
wlt_reverse_48 <= USED 0
wlt_reverse_56 <= USED 0
wlt_reverse_64 <= USED 0
wlt_reverse_bytes <= USED 0
wlt_string_len_for_uint32 <= USED 0
wlt_time_delta <= USED 0
;FILE ymodem.o
Cal_CRC16 <= USED 0
CalcChecksum <= USED 0
FLASH_If_Erase <= USED 0
HAL_Delay <= USED 0
Int2Str <= USED 0
UpdateCRC16 <= USED 0
Ymodem_Transmit <= USED 0
__asm___8_ymodem_c_hcrc____REV16 <= USED 0
__asm___8_ymodem_c_hcrc____REVSH <= USED 0
__asm___8_ymodem_c_hcrc____RRX <= USED 0
ymodem_set_flag <= USED 0
